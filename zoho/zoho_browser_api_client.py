"""
Zoho Browser API Client - 专门用于直接调用 crm.zoho.com 的通用客户端
"""

from typing import Any, Dict, Optional

import httpx
from loguru import logger
from pydantic import BaseModel

from zoho.utils import format_headers


class ZohoBrowserAPIClient:
    """Zoho 浏览器 API 客户端 - 用于直接调用 crm.zoho.com 接口"""

    def __init__(self, timeout: float = 30.0, max_keepalive_connections: int = 10):
        """
        初始化客户端

        Args:
            timeout: 请求超时时间
            max_keepalive_connections: 最大保持连接数
        """
        self.base_url = "https://crm.zoho.com"
        self.timeout = timeout
        self.max_keepalive_connections = max_keepalive_connections
        self._client: Optional[httpx.AsyncClient] = None

    async def _get_client(self) -> httpx.AsyncClient:
        """获取或创建异步客户端"""
        if self._client is None:
            self._client = httpx.AsyncClient(
                base_url=self.base_url,
                timeout=self.timeout,
                limits=httpx.Limits(max_keepalive_connections=self.max_keepalive_connections),
            )
        return self._client

    async def close(self):
        """关闭客户端连接"""
        if self._client:
            await self._client.aclose()
            self._client = None

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()

    async def _make_request(
        self, method: str, endpoint: str, headers: Optional[Dict[str, str]] = None, **kwargs
    ) -> httpx.Response:
        """
        通用请求方法

        Args:
            method: HTTP 方法 (GET, POST, PUT, DELETE)
            endpoint: API 端点
            headers: 请求头
            **kwargs: 其他请求参数

        Returns:
            httpx.Response: 响应对象
        """
        client = await self._get_client()
        clean_headers = format_headers(headers) if headers else {}

        try:
            response = await getattr(client, method.lower())(endpoint, headers=clean_headers, **kwargs)
            return response
        except Exception as e:
            logger.error(f"Zoho Browser API {method.upper()} 请求失败: {e}")
            raise

    async def post(
        self,
        endpoint: str,
        json_data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> httpx.Response:
        """发送 POST 请求"""
        return await self._make_request("post", endpoint, headers, json=json_data)

    async def get(
        self,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> httpx.Response:
        """发送 GET 请求"""
        return await self._make_request("get", endpoint, headers, params=params)

    async def put(
        self,
        endpoint: str,
        json_data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
    ) -> httpx.Response:
        """发送 PUT 请求"""
        return await self._make_request("put", endpoint, headers, json=json_data)

    async def delete(
        self,
        endpoint: str,
        headers: Optional[Dict[str, str]] = None,
    ) -> httpx.Response:
        """发送 DELETE 请求"""
        return await self._make_request("delete", endpoint, headers)


# 全局单例客户端实例
_global_client: Optional[ZohoBrowserAPIClient] = None


def get_zoho_browser_client() -> ZohoBrowserAPIClient:
    """获取全局 Zoho 浏览器 API 客户端实例"""
    global _global_client
    if _global_client is None:
        _global_client = ZohoBrowserAPIClient()
    return _global_client


async def close_global_client():
    """关闭全局客户端连接"""
    global _global_client
    if _global_client:
        await _global_client.close()
        _global_client = None


class ZohoResponseData(BaseModel):
    """Zoho 响应信息"""

    success: bool  # 是否成功
    data: Optional[dict] = None  # 响应数据，仅在 success 为 True 时有效
    code: Optional[str] = None  # 错误码：MANDATORY_NOT_FOUND, DUPLICATE_DATA, ...
    field_name: Optional[str] = None  # 必填字段名称，仅在 code 为 MANDATORY_NOT_FOUND 时有效
    account_id: Optional[str] = None  # 已存在的账户 ID，仅在 code 为 DUPLICATE_DATA 时有效

    def error_message(self, resource_type: Optional[str] = None) -> str:
        """
        获取错误信息

        Args:
            resource_type: 资源类型（account, contact）

        Returns:
            错误信息
        """
        if self.code == "DUPLICATE_DATA":
            return f"{resource_type} already exists" if resource_type else "resource already exists"
        elif self.code == "MANDATORY_NOT_FOUND":
            return f"missing required field: {self.field_name}"
        else:
            return f"unknown error: {self.code}"


async def parse_zoho_response_data(response: httpx.Response) -> ZohoResponseData:
    """
    解析 Zoho CRM API 响应信息
    """
    # 解析响应数据
    response_data_list = response.json().get("data", [])
    if response_data_list and isinstance(response_data_list, list) and len(response_data_list) > 0:
        response_data = response_data_list[0]

        # HTTP 状态码检查
        if response.status_code in (200, 201, 204):
            return ZohoResponseData(success=True, data=response_data)

        # 正常响应
        if response_data.get("status", "") == "success":
            return ZohoResponseData(success=True, data=response_data)

        logger.error(f"response_data_list in parse_zoho_response_data: {response_data_list}")

        # 必填字段缺失场景：多个必填字段缺失，仅会响应第一个
        if response_data.get("code", "") == "MANDATORY_NOT_FOUND":
            field_name = response_data.get("details", {}).get("api_name", "required field")
            return ZohoResponseData(success=False, code="MANDATORY_NOT_FOUND", field_name=field_name)

        # 重复数据场景：资源已存在
        if response_data.get("code", "") == "MULTIPLE_OR_MULTI_ERRORS":
            response_errors = response_data.get("details", {}).get("errors", [])

            if response_errors and isinstance(response_errors, list) and len(response_errors) > 0:
                if response_errors[0].get("code", "") == "DUPLICATE_DATA":
                    account_id = response_errors[0].get("details", {}).get("duplicate_record", {}).get("id", "")
                    return ZohoResponseData(success=False, code="DUPLICATE_DATA", account_id=account_id)

        # 其他错误场景：其他错误
        return ZohoResponseData(success=False, code=response_data.get("code", ""))

    # 未知错误
    raise Exception(response.json().get("message", "unknown error"))
