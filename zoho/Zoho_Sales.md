# 销售辅助工具 Sales Agent 接入 Zoho CRM 说明



### 流程图

地址：https://boardmix.cn/app/share/CAE.CIne_gsgASoQ2bJYNELOk-c6KMehyw7dDTAFQAE/N4xnZN

预览如下：

<iframe style="border: 1px solid rgba(0, 0, 0, 0.1);" sandbox="allow-scripts allow-popups allow-forms allow-modals allow-same-origin" width="800" height="450" src="https://boardmix.cn/app/share/CAE.CIne_gsgASoQ2bJYNELOk-c6KMehyw7dDTAGQAE/N4xnZN" allowfullscreen></iframe>



### 接口定义

1. 事件启动

   `POST /api/sales-agent/start-process`

   请求 Body：

   ```json
   {
     "company_info": {
       "name": "ASP Floors",
       "location": "Australia"
     }
   }
   ```

   参数说明：

   | 参数名                | 是否必须 | 说明                                                         |
   | --------------------- | -------- | ------------------------------------------------------------ |
   | company_info.name     | 是       | 用于查询的公司名称，尽量是完整的，可在 LinkedIn 准确查询到的 |
   | company_info.location | 否       | 所查询的公司所在地                                           |
   
   返回示例：
   
   ```json
   {
     "sessionId": "4be0ebf5-478c-49a0-a624-b793978cc335"
   }
   ```
   
   `sessionId` 是uuid v4 随机生成的当前事件的唯一性标志。

2. 事件交互

`GET /api/sales-agent/events?sessionId=${sessionId}`

请求类型：**eventsource**

参数说明： **sessionId** 是事件启动时返回的**sessionId**

eventsource 返回说明

| 参数名  | 说明                                   |
| ------- | -------------------------------------- |
| type    | 类型                                   |
| total   | 总数                                   |
| message | 消息说明                               |
| data    | 数据返回。不同的 type 返回的数据不同。 |

**type** 说明

| type                 | 说明            | data 说明        |
| -------------------- | --------------- | ---------------- |
| start                | 开始处理数据... |                  |
| latent               | 潜在联系人      |                  |
| filter_latent        | 过滤潜在联系人  |                  |
| get_contacts_info    | 获取联系人信息  |                  |
| update_contacts_info | 更新联系人信息  |                  |
| data_transform       | 数据转换        |                  |
| success              | 完成            | CRM 所需数据格式 |
| error                | 发生错误        |                  |

**返回的CRM数据结构示例：**

```json
[{
    "Newsletter_Signup": "-None-",
    "Country_Territory": "-None-",
    "Exchange_Rate": 1,
    "First_Name": "Grace",
    "Last_Name": "Godwin",
    "Email": "<EMAIL>",
    "Phone": "",
    "Title": "Business Development Director",
    "Department": "Sales",
    "Mobile": "",
    "Function_Type": "Business Senior Management",
    "Secondary_Email": "",
    "LinkedIn": "http://www.linkedin.com/in/grace-godwin-********",
    "Currency": "USD",
    "Labels": "Sales Agent",
    "Associated_Account_Type": "IoT Platform",
    "Associated_Industry": "ICT",
    "Lead_Source": "Linkedin",
    "Twitter": "EROADCareers",
    "Facebook": "https://facebook.com/eroadglobal",
    "Region": "APAC",
    "Description": "EROAD offers fleet management, telematics solutions, and real-time data analytics. The company is based in Auckland, New Zealand, and focuses on driver safety and compliance."
}]
```



### 代码示例

**html 代码**

```html
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>SSE Client</title>
    <style>
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: #f0f0f0;
        }
        .results {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 200px;
        }
        .button {
            padding: 10px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>数据处理系统</h1>
        <div id="status" class="status">准备就绪</div>
        <button id="startButton" class="button" onclick="handleStartProcess()">开始处理</button>
        <div id="results" class="results"></div>
    </div>

    <script>
        // 模拟数据
        const company_info = {
            name: "ASP Floors",
            location: "Australia",
            emails: ["<EMAIL>", "<EMAIL>"],
        }

        // 开始处理按钮点击事件
        const handleStartProcess = async () => {
            try {
                // 准备要发送的数据
                const dataToSend = {
                    company_info: company_info
                };

                // 更新状态
                document.getElementById('status').textContent = '正在处理数据...';
                document.getElementById('results').innerHTML = '';
                document.getElementById('startButton').disabled = true;

                // 发送数据到服务器
                const response = await fetch('http://127.0.0.1:3000/api/sales-agent/start-process', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(dataToSend)
                });

                if (!response.ok) {
                    throw new Error('网络请求失败');
                }

                const data = await response.json();
                const sessionId = data.sessionId;

                // 创建 SSE 连接
                const eventSource = new EventSource(`http://127.0.0.1:3000/api/sales-agent/events?sessionId=${sessionId}`);

                eventSource.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    console.log(`data  on message-> `, data);
                    if (data.type !== 'completed') {
                        const resultsDiv = document.getElementById('results');
                        resultsDiv.innerHTML += `<p>${data.type}</p>`;
                        resultsDiv.scrollTop = resultsDiv.scrollHeight;
                    } else if (data.type === 'completed') {
                        document.getElementById('status').textContent = '处理完成';
                        eventSource.close();
                        document.getElementById('startButton').disabled = false;
                    }
                };

                eventSource.onerror = function() {
                    document.getElementById('status').textContent = '连接错误，请重试';
                    eventSource.close();
                    document.getElementById('startButton').disabled = false;
                };

            } catch (error) {
                console.error('Error:', error);
                document.getElementById('status').textContent = '发生错误，请重试';
                document.getElementById('startButton').disabled = false;
            }
        };
    </script>
</body>
</html>

```

**Deluge 脚本**

```deluge
// 定义公司信息
company_info = map();
company_info.put("name", "ASP Floors");
company_info.put("location", "Australia");

// 创建 HTTP 请求，向 API 发送数据
api_url = "http://127.0.0.1:3000/api/sales-agent/start-process";
headers = map();
headers.put("Content-Type", "application/json");

request_body = map();
request_body.put("company_info", company_info);

response = invokeurl
[
    url : api_url
    type : POST
    parameters: request_body.toString()
    headers: headers
];

if(response.contains("sessionId"))
{
    session_id = response.get("sessionId");
    event_url = "http://127.0.0.1:3000/api/sales-agent/events?sessionId=" + session_id;

    // 监听服务器事件
    event_response = invokeurl
    [
        url : event_url
        type : GET
        headers: headers
    ];

    if(event_response.contains("type") && event_response.get("type") == "completed")
    {
        info "处理完成";
    }
    else
    {
        info event_response.get("type");
    }
}
else
{
    info "请求失败";
}
```

