"""
Zoho utility functions
"""


def format_headers(request_headers: dict) -> dict:
    """
    格式化请求头
    """
    if not request_headers:
        return {}

    clean_headers = {}
    if request_headers:
        for key, value in request_headers.items():
            if isinstance(value, (str, bytes)):
                clean_headers[key] = value
            elif value is not None:
                # 将非字符串值转换为字符串
                clean_headers[key] = str(value)

    return clean_headers
