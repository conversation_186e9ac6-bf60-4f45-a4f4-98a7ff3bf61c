# 流程以及备忘录

# Zoho权限

需要在 https://api-console.zoho.com/ 创建 **Self Client**, 得到 `Client ID` & `Client Secret`, 再 Generate Code 时，Scope 填入 `ZohoCRM.modules.ALL`



## 概念对应

| 常规   | zoho     |
| ------ | -------- |
| 公司   | Accounts |
| 联系人 | Contacts |



## 添加联系人

Notes: **如果 Accounts 不存在，需要先创建**；需要获取到当前用户的 ID 等信息 ，然后将其填入 **Contact Owner**

`url`: https://crm.zoho.com/crm/v2.1/Contacts

`method`: POST

`Payload`:

页面必填项(单个)：

```json
{
    "Currency": "USD",
    "Lead_Source": "-None-",
    "Associated_Industry": "-None-",
    "Associated_Account_Type": "-None-",
    "Newsletter_Signup": "-None-",
    "First_Name": "Paul - 1",
    "Last_Name": "Dunn - 2",
    "Email": "<EMAIL>",
    "Account_Name": {
      "id": "3091799000197361001 - 1",
      "name": "All Ways Wireless Inc. - 1"
    },
    "Function_Type": "Business Management",
    "Exchange_Rate": 1,
    "Owner": {
      "id": "3091799000282499001",
      "full_name": "jian chen",
      "name": "jian chen"
    }
}
```
```json
{
  "data": [
    {
      "Owner": {
        "id": "3091799000282499001",
        "full_name": "jian chen",
        "name": "jian chen"
      },
      "Email_Opt_Out": false,
      "Exchange_Rate": 1,
      "Market_Segment_New": [],
      "Currency": "USD",
      "Lead_Source": "-None-",
      "Associated_Industry": "-None-",
      "Associated_Account_Type": "-None-",
      "Account_Name": {
        "id": "3091799000282972017",
        "name": "Test Integration"
      },
      "Mailing_State": "TEST",
      "First_Name": "test",
      "Newsletter_Signup": "-None-",
      "Last_Name": "test",
      "Layout": {
        "id": "3091799000000091033"
      },
      "Function_Type": "Business Entry",
      "Region": "-None-",
      "Country_Territory": "-None-",
      "$zia_owner_assignment": "owner_recommendation_unavailable",
      "zia_suggested_users": {}
    }
  ],
  "skip_mandatory": false
}
```

`Function_Type List`:

```json
["Business Entry", "Business Management", "Business Senior Management", "Technical Entry", "Technical Management", "Technical Senior Management"]
```

[“业务入门“、”业务管理“、”业务高级管理“、”技术入门“、”技术管理“、”技术高级管理”]

`Associated_Account_Type List`:

```json
["EU(End User)", "OEM", "SI(System Integrator)", "DIS(Distributor)"， "Operator(Fleet or Vending Operator)", "Sales Rep", "Partner", "Others", "Telecom Operator", "IoT Platform", "IoT Partner", "MSP"]
```

[“EU（最终用户）”、“OEM”、“SI（系统集成商）”、“DIS（分销商）”、“运营商（车队或自动售货机运营商）”、“销售代表”、“合作伙伴”、“其他”、“电信运营商”、“物联网平台”、“物联网合作伙伴”、“MSP”]。

`Associated_Industry List`:

```json
["Energy", "Industry", "IWOS", "Commerce", "City", "ICT", "Mobility", "Smart Vending", "Others", "Telecom Operator"]
```

[“能源”、“工业”、“IWOS”、“商业”、“城市”、“信息和通信技术”、“移动”、“智能自动售货机”、“其他”、“电信运营商”]

`Region List`

```json
[  "-None-",  "EMEA",  "NAM",  "LATAM",  "APAC"]
```



完整提交项：

```json
{
  "data": [
    {
      "Owner": {
        "id": "3091799000282499001",
        "full_name": "jian chen",
        "name": "jian chen"
      },
      "Email_Opt_Out": true,
      "Exchange_Rate": 1,
      "Market_Segment_New": [
        "IoT"
      ],
      "Currency": "USD",
      "Lead_Source": "LinkedIn",
      "Associated_Industry": "Others",
      "Associated_Account_Type": "Others",
      "Account_Name": {
        "id": "3091799000282972017",
        "name": "Test Integration"
      },
      "Mailing_State": "TEST",
      "Newsletter_Signup": "No",
      "Region": "EMEA",
      "Country_Territory": "-None-",
      "First_Name": "First Name",
      "Last_Name": "Last Name",
      "Email": "<EMAIL>",
      "Phone": "123321",
      "Title": "DEV",
      "Department": "DEV",
      "Mobile": "123321",
      "Function_Type": "Technical Entry",
      "Secondary_Email": "<EMAIL>",
      "LinkedIn": "https://www.linkedin.com/in/bigfacemaster/",
      "Labels": "Labels",
      "Secondary_Industry": [
        "Others"
      ],
      "Inquiry_Text": "*********",
      "Twitter": "123321",
      "Facebook": "********",
      "Mailing_Street": "12323",
      "Mailing_City": "123",
      "Mailing_Zip": "123",
      "Description": "*********",
      "Layout": {
        "id": "3091799000000091033"
      },
      "$zia_owner_assignment": "owner_recommendation_unavailable",
      "zia_suggested_users": {}
    }
  ],
  "skip_mandatory": false
}
```


`Response`:

```json
{
  "data": [
    {
      "code": "SUCCESS",
      "details": {
        "Modified_Time": "2025-03-19T10:05:11+08:00",
        "Modified_By": {
          "name": "jian chen",
          "id": "3091799000282499001"
        },
        "Created_Time": "2025-03-19T10:05:11+08:00",
        "id": "3091799000282681001",
        "Created_By": {
          "name": "jian chen",
          "id": "3091799000282499001"
        }
      },
      "message": "record added",
      "status": "success"
    }
  ]
}
```

