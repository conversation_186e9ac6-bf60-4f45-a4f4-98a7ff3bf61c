import asyncio
import os

from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import (
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
    SystemMessagePromptTemplate,
)

from config import MODEL
from utils.file_handler import load_file
from utils.logger import get_logger
from utils.models import init_model
from utils.path import get_current_file_directory_path

logging = get_logger(__name__)


FUNCTION_TYPE_LIST = [
    "Business Entry",
    "Business Management",
    "Business Senior Management",
    "Technical Entry",
    "Technical Management",
    "Technical Senior Management",
]

ASSOCIATED_ACCOUNT_TYPE_LIST = [
    "-None-",
    "EU(End User)",
    "OEM",
    "SI(System Integrator)",
    "DIS(Distributor)",
    "Operator(Fleet or Vending Operator)",
    "Sales Rep",
    "Partner",
    "Others",
    "Telecom Operator",
    "IoT Platform",
    "IoT Partner",
    "MSP",
]

ASSOCIATED_INDUSTRY_LIST = [
    "-None-",
    "Energy",
    "Industry",
    "IWOS",
    "Commerce",
    "City",
    "ICT",
    "Mobility",
    "Smart Vending",
    "Others",
    "Telecom Operator",
]
REGION_LIST = ["-None-", "EMEA", "NAM", "LATAM", "APAC"]


# Helper function to clean dictionary values
def clean_dict_values(data: dict) -> dict:
    """Clean dictionary values by converting NaN to empty strings"""
    if not data:
        return {}

    cleaned_data = {}
    for key, value in data.items():
        # Handle None values
        if value is None:
            cleaned_data[key] = ""
        # Handle float NaN
        elif isinstance(value, float) and str(value).lower() == "nan":
            cleaned_data[key] = ""
        # Handle string 'nan'
        elif isinstance(value, str) and value.lower() == "nan":
            cleaned_data[key] = ""
        # Keep other values as is
        else:
            cleaned_data[key] = value

    return cleaned_data


async def summarize_apollo_user_info(apollo_info: dict):
    try:
        """
        Summarize use info and get new data
        """
        # Clean the input data to handle NaN values
        cleaned_apollo_info = clean_dict_values(apollo_info)

        current_file_directory_path = get_current_file_directory_path(__file__)
        system_prompt = load_file(os.path.join(current_file_directory_path, "prompts/summarize.md"))

        # 确保system_prompt不为None
        if system_prompt is None:
            logging.error("Failed to load system prompt from summarize.md")
            system_prompt = """你是一名专业的工业物联网销售专家，专注于分析潜在客户的信息，并总结出关键数据、关键字段等信息。现在你需要将客户信息，总结归纳成 zoho CRM 所需要的内容。"""  # noqa: E501

        user_prompt = "你是一名专业的工业物联网销售专家，专注于分析潜在客户的信息，并总结出关键数据、关键字段等信息。现在你需要将客户信息 {apollo_info}, 总结成 zoho CRM 所需要的内容。其他所需要的数据： {function_type_list}, {associated_account_type_list}, {associated_industry_list}, {region_list}."  # noqa: E501

        system_message_prompt = SystemMessagePromptTemplate.from_template(system_prompt)
        user_message_prompt = HumanMessagePromptTemplate.from_template(user_prompt)

        prompts = ChatPromptTemplate.from_messages([system_message_prompt, user_message_prompt])

        model = MODEL
        if model is None:
            model = "gpt-4o"

        llm = init_model(model)

        chain = prompts | llm | JsonOutputParser()

        result = chain.invoke(
            {
                "apollo_info": cleaned_apollo_info,
                "function_type_list": FUNCTION_TYPE_LIST,
                "associated_account_type_list": ASSOCIATED_ACCOUNT_TYPE_LIST,
                "associated_industry_list": ASSOCIATED_INDUSTRY_LIST,
                "region_list": REGION_LIST,
            }
        )

        # Clean the result to ensure no NaN values are returned
        if result and isinstance(result, dict):
            result = clean_dict_values(result)
        return result
    except Exception as e:
        logging.error(f"Failed to summarize user info: {e}")
        return {}


async def main():
    user_info = {
        "id": "54a4c4097468693676168261",
        "first_name": "Kate",
        "last_name": "Brittain",
        "name": "Kate Brittain",
        "linkedin_url": "http://www.linkedin.com/in/kate-brittain-********",
        "title": "Assistant Manager",
        "email_status": "verified",
        "photo_url": "https://media.licdn.com/dms/image/v2/C4E03AQGTjCGm4o0XCA/profile-displayphoto-shrink_200_200/profile-displayphoto-shrink_200_200/0/*************?e=**********&v=beta&t=991gaR14Go1Yrd9ZaEbl3XrkfuYnL-ScdbhfMarUifY",
        "twitter_url": "",
        "github_url": "",
        "facebook_url": "",
        "extrapolated_email_confidence": "",
        "headline": "Assistant Manager",
        "email": "<EMAIL>",
        "organization_id": "54a11d8569702d7fe6152f01",
        "state": "Queensland",
        "city": "Brisbane",
        "country": "Australia",
        "departments": ["Engineering & Technical", "Information Technology", "Operations"],
        "subdepartments": [],
        "seniority": "manager",
        "functions": [],
        "intent_strength": "",
        "show_intent": False,
        "email_domain_catchall": False,
        "revealed_for_current_team": True,
        "personal_emails": [],
        "about": "I am a passionate  driven individual looking for that next challenge to further my career.",  # noqa: E501
        "thinking": "她是销售骨干，并担任助理经理，具备销售和管理经验，工作积极主动，能影响采购或设备引进决策，非常适合作为物联网和边缘计算产品的切入点。LinkedIn活跃度较高，有进一步沟通的空间。",  # noqa: E501
    }
    new_user_info = await summarize_apollo_user_info(user_info)
    logging.info(f"new user info: {new_user_info}")


if __name__ == "__main__":
    asyncio.run(main())
