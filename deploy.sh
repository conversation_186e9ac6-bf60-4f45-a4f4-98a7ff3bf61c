#!/bin/bash
set -e

# 登录Docker Registry
echo "$DOCKER_REGISTRY_PASSWORD" | docker login $DOCKER_REGISTRY -u $DOCKER_REGISTRY_USER --password-stdin

# 拉取最新镜像
docker pull $DOCKER_REGISTRY/$DOCKER_IMAGE_NAME:$DOCKER_TAG

# 停止并移除旧容器（如果存在）
docker stop sse-service || true
docker rm sse-service || true

# 启动新容器
docker run -d \
  --name sse-service \
  --restart unless-stopped \
  -p 5000:5000 \
  --env-file .env \
  -e "LOG_LEVEL=${LOG_LEVEL:-INFO}" \
  $DOCKER_REGISTRY/$DOCKER_IMAGE_NAME:$DOCKER_TAG

echo "SSE服务已成功部署"