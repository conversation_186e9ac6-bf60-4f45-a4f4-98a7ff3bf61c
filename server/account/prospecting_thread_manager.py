import asyncio
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger
from pymongo import ASCENDING, DESCENDING
from pymongo.collection import Collection

from server.common_types import CurrentUser, StreamMessageType, TaskStatus, TaskType
from server.task.mongodb_client import mongodb_client
from server.task.task_manager import task_manager
from zoho.accounts_api import fetch_accounts_info_by_account_id
from zoho.zoho_notification import send_notification_to_zoho


class ProspectingThreadManager:
    """探查流程管理器，用于管理探查流程下的多个任务"""

    def __init__(self):
        # 使用全局 MongoDB 客户端
        self.db = mongodb_client.db
        self.thread_collection: Collection = self.db["prospecting_threads"]
        # 设置索引
        self._setup_indexes()
        # 存储订阅任务引用
        self._subscribed_tasks: Dict[str, asyncio.Task] = {}

    def _setup_indexes(self) -> None:
        """设置数据库索引"""
        try:
            # 创建索引
            indexes_to_create = [
                {"keys": [("account_id", ASCENDING)], "name": "account_id_idx", "background": True},
                {"keys": [("created_at", DESCENDING)], "name": "created_at_desc_idx", "background": True},
            ]
            for index in indexes_to_create:
                self.thread_collection.create_index(index["keys"], name=index["name"], background=index["background"])
        except Exception as e:
            logger.error("创建索引失败: {}", str(e), exc_info=True)
            raise

    async def _create_prospecting_thread(self, account_id: str, user_query: str) -> Dict[str, Any]:
        """创建新的探查流程"""
        thread_id = str(uuid.uuid4()).replace("-", "")
        now = datetime.now()

        # 创建探查流程数据
        thread_data = {
            "_id": thread_id,
            "account_id": account_id,
            "title": "",
            "user_query": user_query,
            "task_ids": [],
            "created_at": now,
            "updated_at": now,
        }

        # 保存到数据库
        self.thread_collection.insert_one(thread_data)
        logger.info("初始探查流程已创建: {}", thread_id)

        return thread_data

    async def _start_thread_task(
        self, thread_data: Dict[str, Any], account_id: str, user_query: str, current_user: CurrentUser | None = None
    ) -> str:
        """向探查流程添加新的探查任务"""
        try:
            thread_id = str(thread_data.get("_id", ""))

            # 获取所有任务的结果数据，用于排除已探查过的账户
            result_data = await self._get_thread_all_task_results(thread_data=thread_data)
            excludes = [
                data.get("account_info", {}).get("name")
                for data in result_data
                if data.get("account_info", {}).get("name")
            ]

            # 提交探查任务需要的参数
            kwargs = {
                "thread_id": thread_id,
                "user_query": user_query,
                "excludes": excludes,
                "current_user": current_user,
            }
            # 提交探查任务
            task = await task_manager.add_prospecting_accounts_task(account_id=account_id, **kwargs)
            task_id = task.task_id

            # 更新探查流程，添加任务ID
            self.thread_collection.update_one(
                {"_id": thread_id},
                {
                    "$push": {"task_ids": task_id},
                    "$set": {"updated_at": datetime.now()},
                    "$unset": {"error": 1},
                },
            )

            logger.info("向探查流程 {} 添加任务成功: {}", thread_id, task_id)

            # 订阅任务事件流
            await self._subscribe_task_stream(task_id=task_id, current_user=current_user, account_id=account_id)

            return task_id

        except Exception as e:
            logger.error("向探查流程添加任务失败: {}", str(e), exc_info=True)
            raise

    async def _subscribe_task_stream(
        self, task_id: str, current_user: CurrentUser | None = None, account_id: str | None = None
    ):
        """订阅任务事件流"""

        async def _subscribe_task(task_id: str):
            try:
                async for message in task_manager.subscribe(task_id):
                    # 仅处理状态消息
                    if message.type == StreamMessageType.FINISH:
                        if message.error:
                            await self._process_on_task_failed(task_id=task_id)
                        elif message.result:
                            await self._process_on_task_completed(
                                task_id=task_id, current_user=current_user, account_id=account_id
                            )

                        break

            except Exception as e:
                logger.error(f"订阅任务 {task_id} 事件流时出错: {str(e)}", exc_info=True)

        # 创建并保存任务引用
        subscribed_task = asyncio.create_task(_subscribe_task(task_id=task_id))
        subscribed_task.add_done_callback(lambda _: self._subscribed_tasks.pop(task_id, None))
        self._subscribed_tasks[task_id] = subscribed_task

    async def restore_all_running_subscriptions(self):
        """恢复所有未完成任务的事件流订阅"""
        try:
            # 查询所有未完成的探查任务
            task_type = TaskType.PROSPECTING_ACCOUNTS.value
            unfinished_task_ids = await task_manager.list_incompleted_tasks(
                task_type=task_type, tags={"task_type": task_type}
            )
            for task_id in unfinished_task_ids:
                # 避免重复订阅
                if task_id not in self._subscribed_tasks:
                    await self._subscribe_task_stream(task_id=task_id)
        except Exception as e:
            logger.error("恢复所有未完成任务的事件流订阅失败: {}", str(e), exc_info=True)

    def _list_all_threads(self, query: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查询所有探查流程"""
        try:
            # 按创建时间降序排序，最新的在前
            threads = self.thread_collection.find(query).sort("created_at", -1)
            if not threads:
                return []

            threads = list(threads)
            for thread in threads:
                thread["_id"] = str(thread.get("_id", ""))  # 将 _id 转换为字符串
            return threads
        except Exception as e:
            logger.error("查询探查流程失败: {}", str(e), exc_info=True)
            return []

    async def _list_all_tasks(self, thread_data: dict) -> List[Dict[str, Any]]:
        """获取指定探查流程的所有任务"""
        try:
            tags = {
                "account_id": thread_data.get("account_id"),
                "thread_id": str(thread_data.get("_id", "")),
                "task_type": TaskType.PROSPECTING_ACCOUNTS.value,
            }
            task_list = await task_manager.list_all_tasks(tags=tags)
            task_list.reverse()  # 数据按时间顺序（默认查询出来是倒序）
            return task_list
        except Exception as e:
            logger.error("获取指定探查流程的所有任务失败: {}", str(e), exc_info=True)
            return []

    async def _process_on_task_completed(
        self, task_id: str, current_user: CurrentUser | None = None, account_id: str | None = None
    ) -> None:
        """探查任务完成时，更新探查流程数据"""
        task_data = await task_manager.get_task_data(task_id=task_id)
        thread_id = task_data.get("tags", {}).get("thread_id") if task_data else None
        if not thread_id:
            return
        logger.info("{} 任务已完成，正在更新探查流程 {}", task_id, thread_id)

        thread_data = self.thread_collection.find_one({"_id": thread_id})
        if not thread_data:
            logger.error("更新探查流程失败：获取不到 thread_data, thread_id: {}", thread_id)
            return

        # 更新探查流程数据
        updated_fields = {"updated_at": datetime.now()}

        # 没有结果数据时，设置错误信息
        default_title = thread_data.get("user_query", "")
        result_data = task_data.get("result", {}).get("data", [])
        if len(result_data) > 0:
            title = task_data.get("result", {}).get("title")
        else:
            updated_fields["error"] = "No results"
            title = default_title

        # 设置title
        if thread_data.get("title", default_title) != default_title and title:
            updated_fields["title"] = title

        if "error" in updated_fields:
            self.thread_collection.update_one({"_id": thread_id}, {"$set": updated_fields})
        else:  # 当前任务完成时，清除error字段
            self.thread_collection.update_one(
                {"_id": thread_id},
                {"$set": updated_fields, "$unset": {"error": 1}},
            )
        logger.info("更新探查流程数据成功: {}", thread_id)

        # 发送通知
        current_user_email = current_user.email if current_user else None
        if current_user_email:
            # Query account info from zoho
            zoho_account_info = await fetch_accounts_info_by_account_id(account_id)
            #  5 companies have been identified and are viewable under the 'Prospecting' section within the ABC details page.
            notification_message = (
                f"{len(result_data)} companies have been identified and are viewable under the 'Prospecting' section within the {zoho_account_info.get('name', '')} details page."
                if len(result_data) > 0
                else "But no companies have been identified. You can try again."
            )
            await send_notification_to_zoho(
                {
                    "message": f"Your company prospecting task is complete. {notification_message}.",
                    "email": current_user_email,
                    "title": "AI Agent",
                }
            )

    async def _process_on_task_failed(self, task_id: str) -> None:
        """探查任务失败时，更新探查流程数据"""
        task_data = await task_manager.get_task_data(task_id=task_id)
        thread_id = task_data.get("tags", {}).get("thread_id") if task_data else None
        if not thread_id:
            return
        logger.info("{} 任务失败，正在更新探查流程 {}", task_id, thread_id)

        thread_data = self.thread_collection.find_one({"_id": thread_id})
        if not thread_data:
            logger.error("更新探查流程失败：获取不到 thread_data, thread_id: {}", thread_id)
            return

        # 更新探查流程数据
        updated_fields = {"updated_at": datetime.now()}
        updated_fields["error"] = task_data.get("result", {}).get("error", "Task failed")

        if thread_data.get("title", "") == "":
            updated_fields["title"] = thread_data.get("user_query", "")

        self.thread_collection.update_one({"_id": thread_id}, {"$set": updated_fields})
        logger.info("更新探查流程数据成功: {}", thread_id)

    async def _get_thread_all_task_results(self, thread_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取探查流程的所有任务的结果数据"""
        task_list = await self._list_all_tasks(thread_data=thread_data)
        result_data = []
        for task in task_list:
            if task.get("result", {}):
                result_data.extend(task.get("result", {}).get("data", []))
        return result_data

    async def _get_thread_running_task_ids(self, thread_data: Dict[str, Any]) -> Optional[List[str]]:
        """获取探查流程的所有正在运行的任务ID: RUNNING / PENDING"""
        task_list = await self._list_all_tasks(thread_data=thread_data)

        running_task_ids = []
        for task in task_list:
            if task.get("status") in [TaskStatus.RUNNING, TaskStatus.PENDING]:
                running_task_ids.append(task.get("_id"))

        return running_task_ids

    def _temp_fill_title_for_query(self, thread_data: dict) -> dict:
        """查询数据时，临时填充标题字段"""
        if thread_data.get("title", "") == "":
            thread_data["title"] = thread_data.get("user_query", "")
        return thread_data

    async def get_last_task_error(self, thread_data: dict) -> Optional[str]:
        """获取探查流程的最后一个任务的错误信息"""
        task_list = await self._list_all_tasks(thread_data=thread_data)
        if not task_list or len(task_list) < 1:
            return None
        return task_list[-1].get("error", None)

    async def get_prospecting_thread_data(self, thread_id: str) -> Dict[str, Any]:
        """
        获取探查流程信息

        Args:
            thread_id: 探查流程ID

        Returns:
            Dict[str, Any]: 探查流程信息，含所有任务数据
        """
        try:
            thread_data = self.thread_collection.find_one({"_id": thread_id})
            if not thread_data:
                raise ValueError(f"Thread {thread_id} not found")

            # 获取所有正在运行的任务ID: RUNNING / PENDING
            running_task_ids = await self._get_thread_running_task_ids(thread_data=thread_data)

            result = {**self._temp_fill_title_for_query(thread_data), "running_task_ids": running_task_ids}

            # 获取所有任务的结果数据
            result_data = await self._get_thread_all_task_results(thread_data=thread_data)
            if result_data and len(result_data) > 0:
                result["data"] = result_data

            # 获取最后一个任务的错误信息
            last_task_error = await self.get_last_task_error(thread_data=thread_data)
            if last_task_error:
                result["error"] = last_task_error

            return result
        except Exception as e:
            logger.error("获取探查流程信息失败: {}", str(e), exc_info=True)
            raise

    async def list_all_prospecting_threads(self, account_id: str) -> List[Dict[str, Any]]:
        """获取指定账户的所有探查流程"""
        try:
            threads = self._list_all_threads(query={"account_id": account_id})
            threads = [self._temp_fill_title_for_query(thread) for thread in threads]
            return threads
        except Exception as e:
            logger.error("获取账户探查流程列表失败: {}", str(e), exc_info=True)
            return []

    async def create_thread(self, account_id: str, user_query: str, current_user=None) -> Dict[str, Any]:
        """
        创建新的探查流程

        Args:
            account_id: 账户ID
            user_query: 用户要求
            current_user: 当前用户信息

        Returns:
            {
                "thread_id": thread_id,
                "task_id": task_id,
                "task_status": "running",
            }
        """
        # 创建探查流程
        thread_data = await self._create_prospecting_thread(account_id=account_id, user_query=user_query)

        # 启动探查任务
        task_id = await self._start_thread_task(
            thread_data=thread_data,
            account_id=account_id,
            user_query=user_query,
            current_user=current_user,
        )

        return {"thread_id": str(thread_data.get("_id")), "task_id": task_id, "task_status": "running"}

    async def add_thread_task(self, thread_id: str, current_user=CurrentUser | None) -> str:
        """向探查流程中添加新的探查任务

        Args:
            thread_id: 探查流程ID

        Returns:
            {
                "thread_id": thread_id,
                "task_id": task_id,
                "task_status": "running",
            }
        """
        try:
            thread_data = self.thread_collection.find_one({"_id": thread_id})
            if not thread_data:
                raise ValueError(f"Thread {thread_id} not found")

            account_id = thread_data.get("account_id")
            logger.info("开始添加探查任务，account_id: {}, thread_id: {}", account_id, thread_id)

            # 向探查流程中添加任务
            task_id = await self._start_thread_task(
                thread_data=thread_data,
                account_id=account_id,
                user_query=thread_data.get("user_query"),
                current_user=current_user,
            )

            return {"thread_id": thread_id, "task_id": task_id, "task_status": "running"}
        except Exception as e:
            logger.error("添加探查任务失败: {}", str(e), exc_info=True)
            raise


# 创建全局实例
prospecting_thread_manager = ProspectingThreadManager()
