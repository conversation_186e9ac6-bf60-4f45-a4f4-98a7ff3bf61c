import ast
import json
from typing import Any, Awaitable, Callable, Dict, List, Optional, Union
from uuid import UUID

from langchain.callbacks.base import BaseCallbackHandler
from langfuse import get_client
from loguru import logger

from agent.account.research_workflow import execute_research_workflow
from server.task.base_task_processor import BaseTaskProcessor
from zoho.accounts_api import fetch_accounts_info_by_account_id


class StreamEventsCallbackHandler(BaseCallbackHandler):
    """处理事件流的回调"""

    def __init__(self, send_message_callback: Optional[Callable[[str], Awaitable[None]]] = None):
        self._send_message_callback = send_message_callback  # 发送消息的回调函数
        self._research_companies_message = {}  # 所有正在调研的公司的组合消息

    async def _send_message(self, message: str):
        if self._send_message_callback:
            await self._send_message_callback(message)

    async def _send_research_companies_message(self, company_name: str, message: str):
        """发送所有调研公司的组合消息"""
        self._research_companies_message[company_name] = message
        md_message = "The research progress of each company is as follows:"

        for company_name, message in self._research_companies_message.items():
            md_message += f"\n- {company_name}: {message}"

        await self._send_message(md_message)

    def _parse_json_if_needed(self, data: Union[str, Dict[str, Any]]) -> Dict[str, Any]:
        """解析JSON字符串或返回字典"""
        if isinstance(data, str) and data.startswith('{"'):
            try:
                return json.loads(data)
            except Exception:
                return {}
        if isinstance(data, str) and data.startswith("{'"):
            try:
                return ast.literal_eval(data)
            except Exception:
                return {}
        return data if isinstance(data, dict) else {}

    async def on_tool_start(
        self,
        serialized: Optional[Dict[str, Any]],
        input_str: str,
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        tool_input = self._parse_json_if_needed(input_str)

        if isinstance(tool_input, dict) and tool_input.get("explanation"):
            message = tool_input.get("explanation")

            # 处理"research_company"相关工具消息
            if metadata and metadata.get("source") == "research_company" and metadata.get("company_name"):
                await self._send_research_companies_message(metadata.get("company_name"), message)
            else:
                await self._send_message(message)  # 其他工具消息

    async def on_custom_event(
        self,
        name: str,
        data: Any,
        *,
        run_id: UUID,
        tags: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        # 处理通过dispatch_custom_event发送的"thinking"事件
        if name == "thinking" and data and isinstance(data, str):
            await self._send_message(data)

        # 处理"research_company"相关事件
        elif name == "research_company" and data and isinstance(data, dict):
            company_name = data.get("company_name")
            message = data.get("message")
            if company_name and message:
                await self._send_research_companies_message(company_name, message)


class ProspectingAccountsProcessor(BaseTaskProcessor):
    def __init__(self, task_id: str):
        super().__init__(
            task_id=task_id,
            task_name="prospecting_accounts_task",
        )

    async def _arun(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        探查任务执行入口

        Args:
            data: 输入数据，包含 account_id, user_query, excludes 和 current_user

        Returns:
            Dict[str, Any]: 处理结果
        """
        account_id = data.get("account_id")
        if not account_id:
            raise ValueError("missing account_id in input data")

        user_query = data.get("user_query")
        if not user_query:
            raise ValueError("missing user_query in input data")

        langfuse = get_client()
        langfuse.update_current_trace(
            user_id=data.get("current_user", {}).get("email"),
            metadata={
                "account_id": account_id,
            },
        )

        # Query account info from zoho
        zoho_account_info = await fetch_accounts_info_by_account_id(account_id)
        if not zoho_account_info:
            raise ValueError(f"cannot find zoho account info for account_id: {account_id}")

        excludes = data.get("excludes", [])
        logger.info(
            f"Prospecting accounts processor started. user_query: {user_query}, zoho_account_info: {zoho_account_info}, excludes: {excludes}"  # noqa: E501
        )

        # 执行探查agent
        return await execute_research_workflow(
            user_query=user_query,
            company_info=zoho_account_info,
            excludes=excludes,
            callbacks=[],
        )
