import asyncio
import json

from fastapi import APIRouter, HTTPException  # 修改: 添加 Query, Request
from fastapi.responses import StreamingResponse  # 修改: 添加 StreamingResponse
from taskiq import AsyncTaskiqTask

from apollo.apollo_api import enrich_people_phone
from server.common_types import BatchAccountTaskRequest, CompanyInfo, StreamMessage, TaskType
from server.contact_enrichment_requests import contact_enrichment_requests
from server.task.task_manager import task_manager
from zoho.contacts_api import get_contact_by_id, update_contact_by_id

# 创建路由器
router = APIRouter()

from loguru import logger as origin_logger

logger = origin_logger.bind(module_name="server.sse_routes")


@router.post("/api/sales-agent/detect-contacts/start")
async def start_detect_contacts_task(request: CompanyInfo):
    """接收初始请求的端点"""
    data = request.model_dump()

    logger.info(f"开始处理请求: {data}")
    # get account_id from data
    account_id = request.account_id
    custom_prompts = request.custom_prompts
    owner_id = request.owner_id
    current_user = request.current_user
    request_headers = request.request_headers
    if not account_id:
        logger.warning("请求中缺少 account_id")
        return {"error": "请求中缺少 account_id"}

    # 检查数据库中是否存在任务数据
    existing_tasks = await task_manager.list_incompleted_tasks(
        task_type=TaskType.DETECT_CONTACTS.value, tags={"account_id": account_id}
    )

    if existing_tasks:
        # 如果存在未完成的任务，返回错误
        logger.warning(f"账户 {account_id} 已有未完成的任务: {existing_tasks}")
        if existing_tasks[0]:
            return {
                "task_id": existing_tasks[0],
                "messages": f"账户 {account_id} 已有未完成的任务，请等待当前任务完成或取消后再试",
                "notes": "existing_task",
            }
        else:
            raise HTTPException(
                status_code=400,
                detail={
                    "type": "error",
                    "messages": f"账户 {account_id} 已有未完成的任务，请等待当前任务完成或取消后再试",
                },
            )
    try:
        # 检查任务状态
        task: AsyncTaskiqTask[str] = await task_manager.add_detect_contact_task(
            account_id=account_id,
            custom_prompts=custom_prompts,
            owner_id=owner_id,
            current_user=current_user,
            request_headers=request_headers,
        )
        return {"task_id": task.task_id, "status": "running"}
    except Exception as e:
        logger.error(f"提交新任务失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=400,
            detail={"type": "error", "messages": f"提交新任务失败: {str(e)}"},
        )


@router.get("/api/sales-agent/tasks/{task_id}/stream")
async def get_task_stream(task_id: str):
    """SSE连接端点，根据 task_id 执行任务并流式传输事件"""

    def _build_stream_message(message: StreamMessage) -> str:
        return f"data: {json.dumps(message.model_dump(exclude_none=True), ensure_ascii=False, default=str)}\n\n"

    async def event_generator():
        try:
            async for message in task_manager.subscribe(task_id):
                yield _build_stream_message(message)
        except asyncio.CancelledError:
            logger.info(f"Stream for task {task_id} cancelled")
            return
        except Exception as e:
            logger.error(f"为会话 {task_id} 创建流时出错: {e}", exc_info=True)
            message = StreamMessage.from_exception(e)
            yield _build_stream_message(message)

    return StreamingResponse(
        content=event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
            "X-Session-ID": task_id,
        },
    )


@router.get("/api/sales-agent/tasks/{task_id}/cancel")
async def cancel_task(task_id: str):
    """
    根据 task_id 获取任务结果
    """
    await task_manager.cancel_task_by_id(task_id)
    return {"message": "任务已取消"}


@router.get("/api/sales-agent/tasks/{task_id}")
async def get_task_data_by_task_id(task_id: str):
    """
    根据 task_id 获取任务数据
    """
    result = await task_manager.get_task_data(task_id=task_id)
    if result is None:
        raise HTTPException(
            status_code=404,
            detail={"error": "not_found", "messages": "Task result not found"},
        )
    return result


@router.get("/api/sales-agent/account/{account_id}/task")
async def get_account_task_by_account_id(account_id: str):
    """
    根据 account_id 获取任务
    """
    result = await task_manager.get_task_result_by_account_id(account_id=account_id)
    if result is None:
        raise HTTPException(
            status_code=404,
            detail={"error": "not_found", "messages": "Task result not found"},
        )
    return result


@router.post("/api/sales-agent/accounts/tasks")
async def get_account_tasks_by_account_ids(request: BatchAccountTaskRequest):
    """
    根据 account_ids 批量获取任务
    仅返回有任务的账户，无任务的账户不包含在结果中
    """
    tasks = await task_manager.get_tasks_by_account_ids(account_ids=request.account_ids)
    return tasks


@router.get("/api/sales-agent/contact/{contact_id}/enrich/status")
async def get_contact_enrich_status(contact_id: str):
    """
    根据 contact_id 获取联系人扩充信息请求状态

    Return:
    {
        "status": "pending", # 状态: not_found, pending, success, failed
        "updated_at": "2025-06-09T13:27:39.687886" # 更新时间
    }
    """
    contact_enrichment_status = contact_enrichment_requests.get_contact_enrichment_status(contact_id)
    if contact_enrichment_status is None:
        return {"status": "not_found"}
    return {
        "status": contact_enrichment_status["status"],
        "updated_at": contact_enrichment_status["updated_at"],
    }


@router.get("/api/sales-agent/contact/{contact_id}/enrich")
async def enrich_contact_info(contact_id: str):
    """
    根据 contact_id 获取联系人扩充信息（本接口仅返回请求状态）

    Return:
    {
        "status": "pending", # 状态: not_found, pending, success, failed
        "updated_at": "2025-06-09T13:27:39.687886" # 更新时间
    }
    """
    # 校验是否正在获取中
    contact_enrichment_status = contact_enrichment_requests.get_contact_enrichment_status(contact_id)
    if contact_enrichment_status and contact_enrichment_status.get("status", None) == "pending":
        # 扩充请求未完成，不允许重复请求
        raise HTTPException(
            status_code=400,
            detail={"error": "pending", "messages": "Contact is being enriched"},
        )

    # 从zoho中获取联系人信息
    contact_info = get_contact_by_id(contact_id)
    if contact_info is None:
        # 联系人不存在
        raise HTTPException(status_code=400, detail={"error": "not_found", "messages": "Contact not found"})

    # 如果联系人中没有linkedin或email信息，则直接返回异常
    if contact_info is None or (contact_info.get("linkedIn", None) is None and contact_info.get("email", None) is None):
        # 联系人中没有linkedin或email信息
        raise HTTPException(
            status_code=400,
            detail={"error": "not_found", "messages": "Contact's LinkedIn URL or Email not found"},
        )

    # 获取联系人电话信息
    request_id = enrich_people_phone(
        contact_id=contact_id,
        linkedin_url=contact_info.get("linkedIn", None),
        email=contact_info.get("email", None),
    )
    if request_id is None:
        # 扩充请求失败
        raise HTTPException(
            status_code=400,
            detail={"error": "failed", "messages": "Failed to enrich contact"},
        )

    # 更新联系人扩充状态为 pending
    contact_enrichment_status = contact_enrichment_requests.update_contact_enrichment_status(
        contact_id, request_id, "pending"
    )

    return contact_enrichment_status


@router.post("/api/sales-agent-webhook/apollo/{request_id}")
async def handle_webhook(request_id: str, request: dict):
    """处理联系人扩充信息的webhook回调数据"""

    # 需要处理联系人webhook回调数据
    contact_id = contact_enrichment_requests.get_contact_id_by_request_id(request_id)
    logger.info(f"request_id: {request_id} contact_id: {contact_id} 收到webhook回调数据: {request}")
    if contact_id is None:
        logger.warning(f"request_id: {request_id} 不存在，无法处理webhook回调数据")
        return

    status = "failed"
    try:
        people_list = request.get("people", [])
        if not people_list or len(people_list) == 0:
            logger.warning(f"获取到的联系人 {contact_id} 扩充信息为空")
            return

        contact_info = {}
        data = people_list[0]

        # 解析联系人电话信息
        if data.get("status", "") == "success":
            phone_numbers = data.get("phone_numbers", [])
            for phone_number in phone_numbers:
                if phone_number.get("type_cd", "") == "mobile":
                    contact_info["Mobile"] = phone_number.get("sanitized_number", "")
                else:
                    contact_info["Phone"] = phone_number.get("sanitized_number", "")

        if contact_info.get("Mobile", None) is None and contact_info.get("Phone", None) is None:
            logger.warning(f"获取到的联系人 {contact_id} 扩充信息为空")
            return

        try:
            # 调crm接口更新联系人扩充信息
            update_contact_by_id(contact_id, contact_info)
            status = "success"
        except Exception as e:
            logger.error(f"调用 zoho-api 更新联系人 {contact_id} 信息失败: {e}", exc_info=True)

    except Exception as e:
        logger.error(f"处理联系人 {contact_id} 扩充信息的webhook回调数据失败: {e}", exc_info=True)

    finally:
        # 更新联系人扩充状态
        contact_enrichment_requests.update_contact_enrichment_status(contact_id, request_id, status)
