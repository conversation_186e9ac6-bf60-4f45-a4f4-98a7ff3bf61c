import logging

import pymongo
from pymongo import MongoClient
from pymongo.errors import BulkWriteError

from config import MONGODB_COLLECTION_NAME, MONGODB_DB_NAME, MONGODB_URI

BATCH_SIZE = 1000  # 每次处理的文档数量

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


def connect_to_mongodb(uri):
    """建立MongoDB连接"""
    try:
        client = MongoClient(uri)
        # 测试连接
        client.admin.command("ping")
        logger.info("Successfully connected to MongoDB.")
        return client
    except Exception as e:
        logger.error(f"Failed to connect to MongoDB: {e}")
        raise


def migrate_data():
    """执行数据迁移的主函数"""
    client = None
    try:
        client = connect_to_mongodb(MONGODB_URI)
        db = client[MONGODB_DB_NAME]
        collection = db[MONGODB_COLLECTION_NAME]

        logger.info(f"Starting data migration for collection: {MONGODB_COLLECTION_NAME}")

        # --- 步骤 1: 将 events 字段替换成 messages ---
        logger.info("Step 1: Renaming 'events' field to 'messages'...")
        try:
            result = collection.update_many(
                {"events": {"$exists": True}, "messages": {"$exists": False}},
                [{"$set": {"messages": "$events"}}, {"$unset": "events"}],
            )
            logger.info(f"Step 1 completed. Matched: {result.matched_count}, Modified: {result.modified_count}")
        except Exception as e:
            logger.error(f"Error in Step 1: {e}")
            return

        # --- 步骤 2, 3, 4: 在 messages 数组中执行复杂修改 ---
        # 使用游标进行批量处理
        skip = 0
        while True:
            # 查询有 messages 字段的文档
            documents = collection.find({"messages": {"$exists": True, "$type": "array"}}).skip(skip).limit(BATCH_SIZE)

            docs_to_update = list(documents)

            if not docs_to_update:
                break  # 没有更多文档了

            bulk_operations = []
            for doc in docs_to_update:
                messages_modified = False
                if isinstance(doc.get("messages"), list):
                    new_messages = []
                    for msg in doc["messages"]:
                        modified_msg = msg.copy()

                        # --- 步骤 2: 修改 type, name, ts ---
                        # type 转换
                        if modified_msg.get("type") in ["completed", "success"]:
                            modified_msg["type"] = "finish"
                            messages_modified = True
                        elif modified_msg.get("type") and modified_msg.get("type") not in ["finish", "thinking"]:
                            modified_msg["type"] = "thinking"
                            messages_modified = True

                        # "name" 替换为 "content"
                        if "name" in modified_msg:
                            modified_msg["content"] = modified_msg.pop("name")
                            messages_modified = True

                        # "ts" 替换为 timestamp
                        if "ts" in modified_msg:
                            modified_msg["timestamp"] = modified_msg.pop("ts")
                            messages_modified = True

                        # --- 步骤 3 & 4: details 替换为 result, 动态文本 ---
                        if modified_msg.get("type") == "finish":
                            if "details" in modified_msg:
                                # details 替换为 result
                                current_result = modified_msg.pop("details")
                                modified_msg["result"] = current_result
                                messages_modified = True

                                # 步骤 3: added these {len(result.data)} contacts to CRM
                                if isinstance(current_result, dict) and "data" in current_result:
                                    if isinstance(current_result["data"], list):
                                        data_len = len(current_result["data"])
                                        current_result["message"] = f"added these {data_len} contacts to CRM"

                                # 步骤 4: added these {result.total} contacts to CRM
                                if isinstance(current_result, dict) and "total" in current_result:
                                    total_val = current_result["total"]
                                    current_result["message"] = f"added these {total_val} contacts to CRM"

                                modified_msg["result"] = current_result

                        # 重要：将修改后的消息添加到新列表中
                        new_messages.append(modified_msg)

                # 处理文档级别的 result 字段
                if doc.get("result") and isinstance(doc["result"], dict):
                    if "total" in doc["result"]:
                        doc["result"]["content"] = f"added these {doc['result']['total']} contacts to CRM"
                        messages_modified = True

                if messages_modified:
                    update_data = {"messages": new_messages}
                    # 如果文档级别的 result 也被修改了，也要包含在更新中
                    if doc.get("result"):
                        update_data["result"] = doc["result"]

                    bulk_operations.append(pymongo.UpdateOne({"_id": doc["_id"]}, {"$set": update_data}))

            if bulk_operations:
                try:
                    results = collection.bulk_write(bulk_operations, ordered=False)
                    logger.info(f"Batch processed: Matched {results.matched_count}, Modified {results.modified_count}")
                except BulkWriteError as bwe:
                    logger.error(f"BulkWriteError in batch: {bwe.details}")
                except Exception as e:
                    logger.error(f"An unexpected error occurred during bulk write: {e}")
                    break

            skip += BATCH_SIZE

        logger.info("Data migration completed successfully.")

    except Exception as e:
        logger.critical(f"A critical error occurred during migration: {e}")
    finally:
        if client:
            client.close()
            logger.info("MongoDB connection closed.")


if __name__ == "__main__":
    migrate_data()
