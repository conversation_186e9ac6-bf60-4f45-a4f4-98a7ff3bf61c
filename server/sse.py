import asyncio
import sys
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from loguru import logger

from config import (
    SERVER_HOST,
    SERVER_PORT,
)
from server.account.routes import router as account_router
from server.sse_routes import router as sse_router
from server.task.taskiq_app import shutdown, startup
from utils import load_env

# 设置Windows事件循环策略
if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())


@asynccontextmanager
async def lifespan(app: FastAPI):
    load_env()
    """FastAPI lifespan manager for handling startup and shutdown events"""
    logger.remove()

    def loguru_formatter(record):
        """
        Enhanced loguru formatter with comprehensive source information
        """
        message = "<level>{message}</level>"

        # 构建上下文信息
        context_parts = []

        # 添加task_id
        if record["extra"].get("task_id"):
            context_parts.append(f"<magenta>task_id={record['extra']['task_id']}</magenta>")

        # 添加session_id
        if record["extra"].get("session_id"):
            context_parts.append(f"<blue>session_id={record['extra']['session_id']}</blue>")

        # 添加sid (向后兼容)
        if record["extra"].get("sid") and not record["extra"].get("session_id"):
            context_parts.append(f"<blue>sid={record['extra']['sid']}</blue>")

        # 构建上下文字符串
        context_str = f"[{' '.join(context_parts)}] " if context_parts else ""

        # 构建完整消息
        full_message = context_str + message

        # 添加Sales Agent前缀
        prefix = "<yellow>Sales Agent</yellow>"

        # 添加模块级别标识
        # 优先使用extra中的module_name，否则使用record的name
        module_name = record["extra"].get("module_name", record["name"])
        module_display = f"<cyan>[{module_name}]</cyan>"

        return (
            f"{prefix} <green>{{time:YYYY-MM-DD HH:mm:ss.SSS}}</green> "
            "<level>{level: <8}</level>"
            f"{module_display} <cyan>{{function}}</cyan>:<cyan>{{line}}</cyan> - " + full_message + "\n{exception}"
        )

    logger.add(sys.stderr, format=loguru_formatter)

    logger.info("starting taskiq app...")
    await startup()
    logger.info("taskiq app started")

    # 执行数据迁移
    logger.info("starting data migration...")
    try:
        from server.data_merge import migrate_data

        migrate_data()
        logger.info("data migration completed")
    except Exception as e:
        logger.error(f"data migration failed: {e}")
        # 注意：这里我们不会因为数据迁移失败而阻止服务器启动
        # 如果需要阻止启动，可以在这里抛出异常

    yield  # Hand over control to FastAPI

    # close
    logger.info("shutting down taskiq app...")
    await shutdown()
    logger.info("taskiq app closed")


def create_app():
    """创建FastAPI应用实例"""
    # 创建FastAPI应用
    app = FastAPI(lifespan=lifespan)

    # 添加中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 包含SSE路由
    app.include_router(sse_router)
    app.include_router(account_router, prefix="/api/sales-agent")

    @app.exception_handler(Exception)
    async def handle_exception(request: Request, exc: Exception):
        """全局异常处理"""
        logger.bind(module_name="global_exception_handler").error(f"全局异常处理器: {str(exc)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": str(exc), "detail": "服务器内部错误"},
        )

    return app


app = create_app()


def start():
    """启动服务器"""
    # app = create_app()
    uvicorn.run("server.sse:app", host=SERVER_HOST, port=SERVER_PORT, reload=True)


if __name__ == "__main__":
    start()
