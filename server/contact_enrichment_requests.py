from datetime import datetime, timed<PERSON>ta

from pymongo import ASCENDING
from pymongo.collection import Collection

from server.common_types import ContactEnrichmentStatus
from server.task.mongodb_client import mongodb_client
from utils.logger import get_logger

logging = get_logger(__name__)


class ContactEnrichmentRequests:
    """
    contact enrichment requests with mongodb storage.
    """

    def __init__(self):
        """初始化 MongoDB 存储"""
        self.db = mongodb_client.db
        self.collection: Collection = self.db["contact_enrichment_requests"]
        self._setup_indexes()

    def _setup_indexes(self):
        try:
            # 创建 contact_id 索引，用于快速查询联系人
            self.collection.create_index(
                [("contact_id", ASCENDING)],
                name="contact_id_idx",
                background=True,
                unique=True,  # 确保 contact_id 唯一
            )
        except Exception as e:
            logging.error(f"创建索引失败: {e}", exc_info=True)
            raise

    def get_contact_id_by_request_id(self, request_id: str) -> str | None:
        """根据request_id获取contact_id"""
        try:
            contact_request = self.collection.find_one({"request_id": request_id})
            if contact_request is None:
                return None
            return contact_request.get("contact_id", None)
        except Exception as e:
            logging.error(f"根据request_id:{request_id} 获取contact_id失败: {e}", exc_info=True)
            return None

    def get_contact_enrichment_status(self, contact_id: str) -> ContactEnrichmentStatus | None:
        """根据contact_id获取contact_enrichment_status"""
        try:
            contact_enrichment_status = self.collection.find_one({"contact_id": contact_id})
            # 如果状态已过期，则更新状态为failed
            if contact_enrichment_status and self._check_status_has_expired(contact_enrichment_status):
                contact_enrichment_status = self.update_contact_enrichment_status(
                    contact_id, contact_enrichment_status.get("request_id", None), "failed"
                )
            return contact_enrichment_status
        except Exception as e:
            logging.error(f"根据contact_id:{contact_id} 获取contact_enrichment_status失败: {e}", exc_info=True)
            return None

    def _check_status_has_expired(self, contact_enrichment_status: ContactEnrichmentStatus) -> bool:
        """检查status是否已过期（网络等原因导致未收到回调）"""
        if contact_enrichment_status["status"] == "pending":
            now = datetime.now()
            updated_at = contact_enrichment_status["updated_at"]
            if isinstance(updated_at, str):
                updated_at = datetime.fromisoformat(updated_at)
            return now - updated_at > timedelta(minutes=10)  # 10分钟未收到回调，则认为状态已过期
        return False

    def update_contact_enrichment_status(self, contact_id: str, request_id: str, status: str) -> dict | None:
        """更新contact_enrichment_status"""
        try:
            update_data = {"request_id": request_id, "status": status, "updated_at": datetime.now().isoformat()}
            self.collection.update_one(
                filter={"contact_id": contact_id},
                update={"$set": update_data},
                upsert=True,  # 如果记录不存在，则新增记录
            )
            return update_data
        except Exception as e:
            logging.error(f"更新contact_enrichment_status失败: {e}", exc_info=True)
            return None


# create global contact enrichment requests instance
contact_enrichment_requests = ContactEnrichmentRequests()
