import { fetchEventSource } from '@microsoft/fetch-event-source';
import axios from "axios";

document.addEventListener('DOMContentLoaded', function () {
    // 环境设置
    let isLocal = localStorage.getItem('isLocal') === 'true';
    const envSwitch = document.getElementById('env-switch');
    // 初始化开关状态
    envSwitch.checked = !isLocal;
    function updateEnvironment() {
        // 使用环境变量中的配置
        const host = isLocal ? '127.0.0.1' : 'sales-agent.inhand.online'; // 在服务器端可以使用 ENVIRONMENT 变量来决定
        const env = isLocal ? 'Local' : 'Production';
        const envValue = document.getElementById('env-value');
        const envHost = document.getElementById('env-host');
        const envContainer = document.getElementById('env');

        envValue.textContent = env;
        envHost.textContent = `主机: ${host}:3000`;


        // 根据环境添加颜色样式
        if (isLocal) {
            envValue.classList.remove('text-green-600');
            envValue.classList.add('text-blue-600');
            envContainer.classList.remove('bg-green-50');
            envContainer.classList.add('bg-blue-50');
        } else {
            envValue.classList.remove('text-blue-600');
            envValue.classList.add('text-green-600');
            envContainer.classList.remove('bg-blue-50');
            envContainer.classList.add('bg-green-50');
        }

        // 将选择保存到本地存储
        localStorage.setItem('isLocal', isLocal);

        return host;
    }

    // 监听开关变化
    envSwitch.addEventListener('change', function () {
        isLocal = !this.checked;
        updateEnvironment();
    });

    // 初始化环境
    const host = updateEnvironment();



    // 获取 url 中的参数
    const urlParams = new URLSearchParams(window.location.search);


    // 统一管理表单元素状态的函数
    const changeFormItemsStatus = (disable) => {
        const accountIdElms = document.getElementById('account_id');
        const customElms = document.getElementById('prompt');
        const startButton = document.getElementById('startButton');

        // 表单元素列表
        const formElements = [accountIdElms, customElms];

        // 按钮样式类列表
        const buttonActiveClasses = [
            'hover:bg-green-600', 'focus:outline-none', 'focus:ring-2',
            'focus:ring-green-500', 'focus:ring-offset-2', 'bg-green-500'
        ];

        // 为所有表单元素设置状态
        formElements.forEach(element => {
            element.disabled = disable;
            if (disable) {
                element.classList.add('bg-gray-100', 'cursor-not-allowed');
            } else {
                element.classList.remove('bg-gray-100', 'cursor-not-allowed');
            }
        });

        // 设置按钮状态
        startButton.disabled = disable;
        if (disable) {
            startButton.classList.add('bg-gray-400', 'cursor-not-allowed');
            startButton.classList.remove(...buttonActiveClasses);
        } else {
            startButton.classList.remove('bg-gray-400', 'cursor-not-allowed');
            startButton.classList.add(...buttonActiveClasses);
            startButton.innerText = '开始处理';
        }
    }

    // 获取并验证表单数据
    const getFormData = () => {
        const accountId = document.getElementById('account_id').value.trim();
        // 表单验证
        if (!accountId) {
            throw new Error('请填写Account ID');
        }
        // 更新URL参数以便于分享和刷新页面
        const customPrompt = document.getElementById('prompt').value.trim();
        const url = new URL(window.location.href);
        url.searchParams.set('account_id', accountId);
        window.history.replaceState({}, '', url.toString());
        return {
            account_id: accountId,
            custom_prompts: {prompt: customPrompt}
        };

    };

    const resetContents = () => {
        // const sessionIdElement = document.getElementById('session-id');
        // sessionIdElement.textContent = '';
        const tableElms = document.getElementById('data')
        tableElms.classList.add('hidden')
        const dataBody = document.getElementById('data-body');
        dataBody.innerHTML = '';
        const resultsDiv = document.getElementById('results');
        resultsDiv.innerHTML = '';
        const errorDiv = document.getElementById('error');
        errorDiv.innerHTML = '';
        const statusElement = document.getElementById('status');
        statusElement.textContent = '准备就绪';
    }

    // 格式化结果消息
    const formatResultMessage = (data) => {
        const timestamp = new Date().toLocaleTimeString();
        const message = data.message;
        const count = data.data ? data.data.length : 0;

        return `
            <div class="p-2 border-b border-gray-100 last:border-b-0">
                <div class="flex items-center justify-between">
                    <span class="text-gray-700">${message ?? '-'}</span>
                    <span class="text-sm text-gray-500">${timestamp}</span>
                </div>
                ${count > 0 ? `<div class="mt-1 text-sm text-gray-600">数据条数: ${count}</div>` : ''}
            </div>
        `;
    };

    const addDataToTable = (data) => {
        const tableElms = document.getElementById('data');
        tableElms.classList.remove('hidden');
        const dataBody = document.getElementById('data-body');
        dataBody.innerHTML += `<tr class="text-left border-b border-gray-200"><td class="p-2 border-r border-gray-200">${data.First_Name + ' ' + data.Last_Name}</td><td class="p-2 border-r border-gray-200"><a class="text-blue-500" href="mailto:${data.Email}">${data.Email}</a></td><td class="p-2 border-r border-gray-200">${data.Title}</td><td class="p-2">
            <a href="${data.LinkedIn}" class="text-blue-500" target="_blank">${data.LinkedIn}</a></td></tr>`;
    };

    const isEmptyDataRender = () => {
        const dataBody = document.getElementById('data-body');
        dataBody.innerHTML = '';
        dataBody.innerHTML = '<tr><td colspan="4" class="text-center">没有数据</td></tr>';
    }

    // 发送数据到服务器启动处理过程
    const startProcess = async (dataToSend) => {
        // 获取当前环境的主机地址
        const currentHost = isLocal ? '127.0.0.1' : 'sales-agent.inhand.online';


        // 显示当前使用的环境
        console.log(`使用环境: ${isLocal ? 'Local' : 'Production'}, 主机: ${currentHost}`);

        const statusElement = document.getElementById('status');
        const errorElement = document.getElementById('error');
        const resultsDiv = document.getElementById('results');

        const response = await axios.post(
            `http://${currentHost}:3000/api/sales-agent/start`,
            JSON.stringify(dataToSend),
            {
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        )
        console.log(`response -> `, response);
        const { data } = response
        const {task_id, detail, status } = data

        console.log(`status ${status}`);
        console.log(`task_id ${task_id}`);

        if (status === 'running') {
            const ctrl = new AbortController();
            try {
                await fetchEventSource(`http://${currentHost}:3000/api/sales-agent/tasks/${task_id}/stream`, {
                headers: {
                    'Content-Type': 'application/json',
                },
                signal: ctrl.signal,
                // body: JSON.stringify(dataToSend),
                onmessage(event) {
                    try {
                        // 预处理JSON字符串，替换NaN为null
                        const sanitizedData = event.data
                            .replace(/: *NaN/g, ': null')
                            .replace(/: *Infinity/g, ': null')
                            .replace(/: *-Infinity/g, ': null');

                        const data = JSON.parse(sanitizedData);
                        console.log(`date ${new Date().toISOString()}, data on message-> `, data);

                        resultsDiv.innerHTML += formatResultMessage(data);
                        resultsDiv.scrollTop = resultsDiv.scrollHeight;

                        // 处理数据转换类型的消息
                        if (data.type === 'data_transform' && data.data?.length > 0) {
                            addDataToTable(data.data?.[0]);
                        }

                        // 处理完成或成功类型的消息
                        if (data.type === 'completed' || data.type === 'success') {
                            statusElement.textContent = '处理完成';
                            if (data?.data?.length === 0 || data?.data?.length === undefined) {
                                isEmptyDataRender();
                            }
                            changeFormItemsStatus(false);
                        }

                        // 处理错误类型的消息
                        if (data.type === 'error') {
                            errorElement.classList.remove('hidden');
                            statusElement.textContent = '处理失败';
                            changeFormItemsStatus(false);
                            errorElement.innerHTML += formatResultMessage(data);
                            errorElement.scrollTop = errorElement.scrollHeight;
                        }
                    } catch (error) {
                        console.error('Error parsing SSE message:', error);
                        console.error('Raw message data:', event.data);
                        statusElement.textContent = '消息解析错误';
                        changeFormItemsStatus(false);
                    }
                },
                onerror(error) {
                    console.error('SSE connection error:', error);
                    statusElement.textContent = '连接错误，请重试';
                    changeFormItemsStatus(false);
                    ctrl?.abort();
                },
                onclose() {
                    console.log('Connection closed');
                    ctrl.abort()
                }
            });
        } catch (error) {
            console.error('Error:', error);
            ctrl.abort()
            statusElement.textContent = error.message || '发生错误，请重试';
            changeFormItemsStatus(false);
            errorElement.classList.remove('hidden');
            errorElement.innerHTML = `
                <div class="p-2 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <span class="text-red-600 font-medium">错误</span>
                        <span class="text-sm text-gray-500">${new Date().toLocaleTimeString()}</span>
                    </div>
                    <div class="mt-1 text-sm text-red-600">${error.message || '未知错误'}</div>
                </div>
            `;
        }} else if (detail?.type === "existing_task") {
            const taskIds = detail?.task_ids;
            if (taskIds?.length > 0) {
                console.log(taskIds)
            }
            changeFormItemsStatus(false);
        }
    };

    // 开始处理按钮点击事件
    const handleStartProcess = async (event) => {
        // 防止表单默认提交
        event.preventDefault();
        try {
            // 重置界面内容
            resetContents();
            // 禁用表单和按钮
            changeFormItemsStatus(true);
            const btnElement = document.getElementById('startButton');
            const statusElement = document.getElementById('status');
            const resultsElement = document.getElementById('results');
            const errorElement = document.getElementById('error');

            // 获取表单数据
            const company_info = getFormData();
            console.log("company_info", company_info)

            // 更新状态
            statusElement.textContent = '正在处理数据...';
            resultsElement.innerHTML = '';
            btnElement.innerText = '正在处理...'
            errorElement.classList.add('hidden');

            // 直接调用处理过程
            await startProcess(company_info);

        } catch (error) {
            console.error('Error:', error);
            const statusElement = document.getElementById('status');
            statusElement.textContent = error.message || '发生错误，请重试';
            changeFormItemsStatus(false);
            // 显示错误信息
            const errorElement = document.getElementById('error');
            errorElement.classList.remove('hidden');
            errorElement.innerHTML = `
                <div class="p-2 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <span class="text-red-600 font-medium">错误</span>
                        <span class="text-sm text-gray-500">${new Date().toLocaleTimeString()}</span>
                    </div>
                    <div class="mt-1 text-sm text-red-600">${error.message || '未知错误'}</div>
                </div>
            `;
        }
    };

    const handleQueryResult = async (event) => {
        event.preventDefault();
        try {
            const account_id = document.getElementById('account_id').value.trim();
             // 获取当前环境的主机地址
            const currentHost = isLocal ? '127.0.0.1' : 'sales-agent.inhand.online';
            const response = await axios.get(`http://${currentHost}:3000/api/sales-agent/${account_id}/result`);
            console.log("response", response);
        } catch (error) {
            console.error('Error:', error);
            const statusElement = document.getElementById('status');
            statusElement.textContent = error.message || '发生错误，请重试';
            changeFormItemsStatus(false);
            // 显示错误信息
        }
    }

    // 添加按钮点击事件监听器
    document.getElementById('startButton').addEventListener('click', handleStartProcess);


    document.getElementById('query_result').addEventListener('click', handleQueryResult);
}); // 关闭DOMContentLoaded事件监听器
