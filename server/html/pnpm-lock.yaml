lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@microsoft/fetch-event-source':
        specifier: ^2.0.1
        version: 2.0.1
      axios:
        specifier: ^1.9.0
        version: 1.9.0
    devDependencies:
      buffer:
        specifier: ^6.0.3
        version: 6.0.3
      parcel:
        specifier: ^2.15.1
        version: 2.15.1(@swc/helpers@0.5.17)

packages:

  '@lezer/common@1.2.3':
    resolution: {integrity: sha512-w7ojc8ejBqr2REPsWxJjrMFsA/ysDCFICn8zEOR9mrqzOu2amhITYuLD8ag6XZf0CFXDrhKqw7+tW8cX66NaDA==}

  '@lezer/lr@1.4.2':
    resolution: {integrity: sha512-pu0K1jCIdnQ12aWNaAVU5bzi7Bd1w54J3ECgANPmYLtQKP0HBj2cE/5coBD66MT10xbtIuUr7tg0Shbsvk0mDA==}

  '@lmdb/lmdb-darwin-arm64@2.8.5':
    resolution: {integrity: sha512-KPDeVScZgA1oq0CiPBcOa3kHIqU+pTOwRFDIhxvmf8CTNvqdZQYp5cCKW0bUk69VygB2PuTiINFWbY78aR2pQw==}
    cpu: [arm64]
    os: [darwin]

  '@lmdb/lmdb-darwin-x64@2.8.5':
    resolution: {integrity: sha512-w/sLhN4T7MW1nB3R/U8WK5BgQLz904wh+/SmA2jD8NnF7BLLoUgflCNxOeSPOWp8geP6nP/+VjWzZVip7rZ1ug==}
    cpu: [x64]
    os: [darwin]

  '@lmdb/lmdb-linux-arm64@2.8.5':
    resolution: {integrity: sha512-vtbZRHH5UDlL01TT5jB576Zox3+hdyogvpcbvVJlmU5PdL3c5V7cj1EODdh1CHPksRl+cws/58ugEHi8bcj4Ww==}
    cpu: [arm64]
    os: [linux]

  '@lmdb/lmdb-linux-arm@2.8.5':
    resolution: {integrity: sha512-c0TGMbm2M55pwTDIfkDLB6BpIsgxV4PjYck2HiOX+cy/JWiBXz32lYbarPqejKs9Flm7YVAKSILUducU9g2RVg==}
    cpu: [arm]
    os: [linux]

  '@lmdb/lmdb-linux-x64@2.8.5':
    resolution: {integrity: sha512-Xkc8IUx9aEhP0zvgeKy7IQ3ReX2N8N1L0WPcQwnZweWmOuKfwpS3GRIYqLtK5za/w3E60zhFfNdS+3pBZPytqQ==}
    cpu: [x64]
    os: [linux]

  '@lmdb/lmdb-win32-x64@2.8.5':
    resolution: {integrity: sha512-4wvrf5BgnR8RpogHhtpCPJMKBmvyZPhhUtEwMJbXh0ni2BucpfF07jlmyM11zRqQ2XIq6PbC2j7W7UCCcm1rRQ==}
    cpu: [x64]
    os: [win32]

  '@microsoft/fetch-event-source@2.0.1':
    resolution: {integrity: sha512-W6CLUJ2eBMw3Rec70qrsEW0jOm/3twwJv21mrmj2yORiaVmVYGS4sSS5yUwvQc1ZlDLYGPnClVWmUUMagKNsfA==}

  '@mischnic/json-sourcemap@0.1.1':
    resolution: {integrity: sha512-iA7+tyVqfrATAIsIRWQG+a7ZLLD0VaOCKV2Wd/v4mqIU3J9c4jx9p7S0nw1XH3gJCKNBOOwACOPYYSUu9pgT+w==}
    engines: {node: '>=12.0.0'}

  '@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.3':
    resolution: {integrity: sha512-QZHtlVgbAdy2zAqNA9Gu1UpIuI8Xvsd1v8ic6B2pZmeFnFcMWiPLfWXh7TVw4eGEZ/C9TH281KwhVoeQUKbyjw==}
    cpu: [arm64]
    os: [darwin]

  '@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.3':
    resolution: {integrity: sha512-mdzd3AVzYKuUmiWOQ8GNhl64/IoFGol569zNRdkLReh6LRLHOXxU4U8eq0JwaD8iFHdVGqSy4IjFL4reoWCDFw==}
    cpu: [x64]
    os: [darwin]

  '@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.3':
    resolution: {integrity: sha512-YxQL+ax0XqBJDZiKimS2XQaf+2wDGVa1enVRGzEvLLVFeqa5kx2bWbtcSXgsxjQB7nRqqIGFIcLteF/sHeVtQg==}
    cpu: [arm64]
    os: [linux]

  '@msgpackr-extract/msgpackr-extract-linux-arm@3.0.3':
    resolution: {integrity: sha512-fg0uy/dG/nZEXfYilKoRe7yALaNmHoYeIoJuJ7KJ+YyU2bvY8vPv27f7UKhGRpY6euFYqEVhxCFZgAUNQBM3nw==}
    cpu: [arm]
    os: [linux]

  '@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3':
    resolution: {integrity: sha512-cvwNfbP07pKUfq1uH+S6KJ7dT9K8WOE4ZiAcsrSes+UY55E/0jLYc+vq+DO7jlmqRb5zAggExKm0H7O/CBaesg==}
    cpu: [x64]
    os: [linux]

  '@msgpackr-extract/msgpackr-extract-win32-x64@3.0.3':
    resolution: {integrity: sha512-x0fWaQtYp4E6sktbsdAqnehxDgEc/VwM7uLsRCYWaiGu0ykYdZPiS8zCWdnjHwyiumousxfBm4SO31eXqwEZhQ==}
    cpu: [x64]
    os: [win32]

  '@parcel/bundler-default@2.15.1':
    resolution: {integrity: sha512-AAOomjOWAhvwunN7hwxmYoAyePlDyOrd0HVUQBJyRhHb6udAPCoq0TDWZ98xybvfKjjbPidk/lVAVZf5A8TyQw==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/cache@2.15.1':
    resolution: {integrity: sha512-kj/yn21Fn4HBrQATLV6k18T3PJTzTiDMVVh0C/gd/21e0ApTlDgvpIw2tiGajZYTluiDEmAm05IqULGhupo9iw==}
    engines: {node: '>= 16.0.0'}
    peerDependencies:
      '@parcel/core': ^2.15.1

  '@parcel/codeframe@2.15.1':
    resolution: {integrity: sha512-Ma4mGvecXh9bbpOKUFDLMjbTeEkPge233e4kEYEp0cU4MVnnUkohhEDUV8tE04wJ+sbyRF9MB4LvfYJEVdqc9A==}
    engines: {node: '>= 16.0.0'}

  '@parcel/compressor-raw@2.15.1':
    resolution: {integrity: sha512-OmQEVFlAX7480xQc6KgFf1R3kkv8o3vTHmHCGk6NIXvtmkxC5zzeYl9lEPPGl4hG3GRGw9CP7Kv0k+emUp1q2A==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/config-default@2.15.1':
    resolution: {integrity: sha512-Ckm0LkNbzGmRNM9SU17rIowAEhSSL4MVXGO+pNCa6Eg2azwDKc7OUq8h9prCJ8rLaSAF0gvLQqhQdM9KBmwozw==}
    peerDependencies:
      '@parcel/core': ^2.15.1

  '@parcel/core@2.15.1':
    resolution: {integrity: sha512-GGx7ht8Qh2InXoBsfIzS6THKGtMREgUDKI2vlzJDnm7OxZdVHn7KdYzG4w6/UKA70lFVr3SMOCdyk2EfRE48CQ==}
    engines: {node: '>= 16.0.0'}

  '@parcel/diagnostic@2.15.1':
    resolution: {integrity: sha512-UJFMUUHuB0YMf9V3dIlsyf1iq4pK/28ryIrI5hK3OiRwrtV2J986ksMeHzUHK/XVtn/8OhFh5tjkQUzKdb8fCw==}
    engines: {node: '>= 16.0.0'}

  '@parcel/error-overlay@2.15.1':
    resolution: {integrity: sha512-ljuYuotFr+24r3m3x/xFUvKFudHtV1cPbhTGuzp3AnRFOk66nLXsAIso9Vmj57GdwYtzW2/b9J0OjC4lZB19sg==}
    engines: {node: '>= 16.0.0'}

  '@parcel/events@2.15.1':
    resolution: {integrity: sha512-esCmICgD/OWUjqhgJv9bYDsIjpKP+Hcg0jIuJqY/M0PLnUn1+hg/v6BZ6IFQ56gh2F1ShORKVPcayF2Etn7dEQ==}
    engines: {node: '>= 16.0.0'}

  '@parcel/feature-flags@2.15.1':
    resolution: {integrity: sha512-NdG5O8XJBFzeJXbjmt1JJjdQiCgNNfhoWlt5ZSyyrS5/5BgNQo0022XNidN1kEdSrcItvE4OPEd0NV5y/t7zNA==}
    engines: {node: '>= 16.0.0'}

  '@parcel/fs@2.15.1':
    resolution: {integrity: sha512-ycm/MPTUM/RonuIqTHGrSxeIz6ZOPpyWzVXuc37dq9eR/kSIHLCAWxhtr7nVrZyrStX/ARUC6aQUqRBg6DDJMg==}
    engines: {node: '>= 16.0.0'}
    peerDependencies:
      '@parcel/core': ^2.15.1

  '@parcel/graph@3.5.1':
    resolution: {integrity: sha512-28mwhl5smB/G6bIIAtV/GHD98qAAKhm3gWkuwFua+gIhOscsiao0jIVHvYwfcQewbesJrvXa8Xr4Jwqkl+4dGw==}
    engines: {node: '>= 16.0.0'}

  '@parcel/logger@2.15.1':
    resolution: {integrity: sha512-oAmZDBiX8DmRWkxvNuEOGTcT0IQzlHvJCg+VtEHkprclAeHAzymT+uKfkvnjCfw3WRUdrrOqXPli3mfBqcHoQQ==}
    engines: {node: '>= 16.0.0'}

  '@parcel/markdown-ansi@2.15.1':
    resolution: {integrity: sha512-Ja+N6B1/JABUiOMuCDFw/qZAiErJgNOyUR0dF7o32xbWsw+PQlRLSbhpaq/97YZCN6bit2BPQD6sfuIqFQHmfQ==}
    engines: {node: '>= 16.0.0'}

  '@parcel/namer-default@2.15.1':
    resolution: {integrity: sha512-yrx8TvyEhrqW+ioO7wZyN4BO0EBQdl5cOIzm/STYIlyQamikDAq8CCZ0ZCRdMNh4bm3K/l5gPY+On2t1SBZ1Ag==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/node-resolver-core@3.6.1':
    resolution: {integrity: sha512-CwP01sMXn75GUmBSL+GdVlvhMWcjSr7vvARobG0vM8VYPduZet0AdqYYoZ0alkk43boDV/HmZJnoucKQIk75Tw==}
    engines: {node: '>= 16.0.0'}

  '@parcel/optimizer-css@2.15.1':
    resolution: {integrity: sha512-7fbw+GLIntxN73SadfBUr+Mc+K5EvOIAshkZmJ5ikKg6RAPpngI8v11AYWBIdfHheHPIopGbHgJjG45bzicxqg==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/optimizer-html@2.15.1':
    resolution: {integrity: sha512-vdwql378Thzfg5S5KwfQcZbuZjmD9/NSg/vpfoYh27x9Mg7D1NSRo9NdUJtvflUqU4ukARW26Ab/S2ENZnNzbQ==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/optimizer-image@2.15.1':
    resolution: {integrity: sha512-wBTuGYvDeGBsY2zrnILZGIhCaIWdcxrW5QdeJcE7U0t9Cf+ybw7Er2qoY1D+UFHTn65pv16WU5aXW6d6Jkczew==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}
    peerDependencies:
      '@parcel/core': ^2.15.1

  '@parcel/optimizer-svg@2.15.1':
    resolution: {integrity: sha512-SQkqec2jnvwRZfBG8s8V5/3BH31jzxCrT9tW9udP2oIx4bLPz4o3ksOCBCVBqdOk1irJiM8KbGr6pHh4YKHjpg==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/optimizer-swc@2.15.1':
    resolution: {integrity: sha512-4KssFZUza2wzD6xrPQAKcyfgPejIueNnfZKA0vlMCBru0uWXsDqvRgRqzNtNUYzDzIThTh5dU7dZP7V1Hy8GKQ==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/package-manager@2.15.1':
    resolution: {integrity: sha512-/B8Nk1md6eG7Z7MLzr+B1Z5Hne1ssNXaJVdhTyxN2FWmV+W7YfZId1Js6CZzQRUElLdFD5tOMcOKCCcNuVjjyw==}
    engines: {node: '>= 16.0.0'}
    peerDependencies:
      '@parcel/core': ^2.15.1

  '@parcel/packager-css@2.15.1':
    resolution: {integrity: sha512-ElwNtT/ZmamGqT0H6DuVeqs33pCj3HAiOKvwT7DRoErOH+W3PNOjpscr8zinnGJsK47ULpHZgvSq2prD6F3JpQ==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/packager-html@2.15.1':
    resolution: {integrity: sha512-TQrvsr00IjALV9bfAWYPjjf6Tii58XS7oEyrMayMfbem2uzFl6gY+Y4kDdaAKTRNZuJggb58ypsOG3OKLpkHhQ==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/packager-js@2.15.1':
    resolution: {integrity: sha512-afQThQpB3j+GLsRTsOABIi9QDBgGZfZQn4etMdFrnT9rSikRxFn5CStJnPc7jgnUf4deVOoODbVXF8VAYl1nUw==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/packager-raw@2.15.1':
    resolution: {integrity: sha512-MZWaEZ1EJAw5R5w1ZPeoDHcwtmYfAHBm89r0F9gkUAhAxvAzSUDoFt8Cnlfz4i+lQW5ZMhUtFXRnD9n9xSkfjw==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/packager-svg@2.15.1':
    resolution: {integrity: sha512-LVof+T0UmrqM5GUdrUKyBTuYaPt6E1QmEk08QgcAWCDCjBN/xq9UDpSBSY+erOm7YIA0ad8YkyqANJMPP6o2pA==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/packager-wasm@2.15.1':
    resolution: {integrity: sha512-n3ZCAep6cMhXxjunUytIMpaPR7+9GoB80RyYFQzRhSEorDYmgInDbvYjAFDtI4XHr3aRPLn67CTSMC0OQa6U5Q==}
    engines: {node: '>=16.0.0', parcel: ^2.15.1}

  '@parcel/plugin@2.15.1':
    resolution: {integrity: sha512-nlcmKLrEfV0ZJ6Ow6Wt7HlcSBXPd4o5h8u2VSHVapP/4ZfXUSi3RMELQ1vNuZH0+kWPPGCwRCjcDqMa7FZ5q7w==}
    engines: {node: '>= 16.0.0'}

  '@parcel/profiler@2.15.1':
    resolution: {integrity: sha512-/FiXLrydaLO3RuzKsxCdNGekFPUmG7xCaseW9uDIraQJl39TD4Yxr8R8X3TOuxCvxNSfci87YSenQGXiOlqCMg==}
    engines: {node: '>= 16.0.0'}

  '@parcel/reporter-cli@2.15.1':
    resolution: {integrity: sha512-w/xFyiQNx/PPREimNgA+ZzrubQmtU1nPqQzOtUwvIGdPsp2BMc6VuM1mmmO9On0awX1lfKlWJvj7A6CZ7ZOrAA==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/reporter-dev-server@2.15.1':
    resolution: {integrity: sha512-5b2ESpM6lgXSH+UIJKafLeOLsUkEtiUC3EMnvAS0frt4DyC5wtR4I9us+SO4OPm2ah7xkxDJI6mIjet5IBBioQ==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/reporter-tracer@2.15.1':
    resolution: {integrity: sha512-Je2m/PMx3UJM+NZ1JFiMvwiZXSOmJ2jCvoy7y+kBAjZ95JZTjxK/dMrjMAJNG0iTPdrgfSOez/R131OdqbGOsg==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/resolver-default@2.15.1':
    resolution: {integrity: sha512-32/hmXv+HZxM6Sr8/bxugCHXK5/za+sm8Jpni1hH24O1MN4sw/6lxRawOmshaifsHe634xi8JZRxKCby0CK8eQ==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/runtime-browser-hmr@2.15.1':
    resolution: {integrity: sha512-XuhRY4eexys7H0186oiIfutrcGBHn//4pPXWCAd740A5PrgXfRezNG/aRQ8xSkZ9BwpshWfv1b9zyONHex5tdA==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/runtime-js@2.15.1':
    resolution: {integrity: sha512-hXbHXB7dgBQW6mxnGgkhtOt+pLnr1HXGitLGxaQZSX0sbepPK0uY8U959Wkxnw7Tu5ru76CSfnP+1ZyZGE6Z0A==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/runtime-rsc@2.15.1':
    resolution: {integrity: sha512-5UIj09IUj0+v9uyQrIGPDTkM5RLJEOgGsS8sHtfDZ1SFOjIumbT4/J0tXF0QDNrsvBdDx9dxlxRvwyyEehUvkg==}
    engines: {node: '>= 12.0.0', parcel: ^2.15.1}

  '@parcel/runtime-service-worker@2.15.1':
    resolution: {integrity: sha512-SzoGHsHIp93FmGbs6R5IjSbGnIkPit9tbbV1wZPW7xzdO9vjOjcWN1pw2t0I5Pdm50b0uf3qEYyIcd+gfNidsA==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/rust-darwin-arm64@2.15.1':
    resolution: {integrity: sha512-QkAnDhoc44gMLzI7o3S5y7kmZBdAQHvR2WyroE6ROrbu/ErR2dHwUenFKMLO297H7rb4LvckAbAHt5OqeS/Mww==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [darwin]

  '@parcel/rust-darwin-x64@2.15.1':
    resolution: {integrity: sha512-brd9Y+UdSXOoOmP9QEQQ/mMWSF57gozlyZPdPkpSgfoCVWSF3/9WANumL8dCGve+JlbMF95eg2H9hcXdMubfaw==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [darwin]

  '@parcel/rust-linux-arm-gnueabihf@2.15.1':
    resolution: {integrity: sha512-06G8pXdpkPZ3zOhHMWoGBU5v/iV9ucKEtNA6NCA48M4t9nQP0HkcUjAulrflq4B5HsAYkG4691Dw52znXWISDQ==}
    engines: {node: '>= 10'}
    cpu: [arm]
    os: [linux]

  '@parcel/rust-linux-arm64-gnu@2.15.1':
    resolution: {integrity: sha512-BMEmCkWzHSaLwfY2Mcsi/eXWvSqQf3IOKll2jRVuS2cZQambyggunXD5prrvj++gXN1cwpGmno2BLDoA08Vwzg==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@parcel/rust-linux-arm64-musl@2.15.1':
    resolution: {integrity: sha512-6FfxmBUFIRFJpHghx+ybghFaxKtKgwDX/9NUYYrqdv83snT9aEkYU0pJqfRyrdz2Wkr1tkOeIhgtvo53CksD1w==}
    engines: {node: '>= 10'}
    cpu: [arm64]
    os: [linux]

  '@parcel/rust-linux-x64-gnu@2.15.1':
    resolution: {integrity: sha512-EV0ATUcUP3XY2ej0f+T/YJVMMpgCS/ppGuoykqv1VmopNz4mhXQIFezF6QnI9WUOJDgFUg/S9lC4GJGfmy0Dzg==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@parcel/rust-linux-x64-musl@2.15.1':
    resolution: {integrity: sha512-PffkN//0SZsAd7VM3Ywr7DMw22Fg7S//rn7e/DjZG6llCYYwQLjQOcZXb/B8NdzfNs3cp1L6U2muwYxMpPOfOQ==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [linux]

  '@parcel/rust-win32-x64-msvc@2.15.1':
    resolution: {integrity: sha512-CX/SgV2jdkJT+ySZpq1UDkhwI7Sg1qUTWvSrD+ApPcYoO9dqDd+Amqr3XIUyyNfSlqnHvDsGtQNBVjkHBrPzdw==}
    engines: {node: '>= 10'}
    cpu: [x64]
    os: [win32]

  '@parcel/rust@2.15.1':
    resolution: {integrity: sha512-1YRgpvSFDJPvybC2YWQrcYzHz8gvb3SajsbriZETdvUu6N1OfG75UJHfJ8Mb+NokOrQNhVRrrGX5Q3ELEMuaxg==}
    engines: {node: '>= 16.0.0'}
    peerDependencies:
      napi-wasm: ^1.1.2
    peerDependenciesMeta:
      napi-wasm:
        optional: true

  '@parcel/source-map@2.1.1':
    resolution: {integrity: sha512-Ejx1P/mj+kMjQb8/y5XxDUn4reGdr+WyKYloBljpppUy8gs42T+BNoEOuRYqDVdgPc6NxduzIDoJS9pOFfV5Ew==}
    engines: {node: ^12.18.3 || >=14}

  '@parcel/transformer-babel@2.15.1':
    resolution: {integrity: sha512-VXMOYfy1/VkQxZP3wbkHbqRXrVEA0yWLNWGxND8MhCdIyEb/ry0J+RZLBB+fa/uBV5S8HI3VYFtVRKExrdKYPg==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/transformer-css@2.15.1':
    resolution: {integrity: sha512-vI6JO/+qyAjL0ah+J8tZrGHMf82PC98+iTRzEFCBrexwICHrfiYkJH8bTRDYQPCsFcK8GZNGnLV6QyRkKQyqzg==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/transformer-html@2.15.1':
    resolution: {integrity: sha512-c9k6NO3DpVughTTbYcWy8DIABezlYYhi274mKnIaT0t3ETZ9UBepyY1KhNU++imhk64hfvUAvJ/ZwWRpYGnV3g==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/transformer-image@2.15.1':
    resolution: {integrity: sha512-S5dsK/w3aS0u0jAMqWiadqjxa/Zl8dW0DDlNwPq7LOnvlcgPnbXrbYVzbmF0ylMtY+ZE0FUHsKAO8zmZe8vi0Q==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}
    peerDependencies:
      '@parcel/core': ^2.15.1

  '@parcel/transformer-js@2.15.1':
    resolution: {integrity: sha512-2rxuc9kvGhkhsEabHUKkxlsYsrquhAmwdgpiAjGYMMzLIRR/xyc6dJWoDEBmrARUrw6XBRBMz61rlVhtwM+nBw==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}
    peerDependencies:
      '@parcel/core': ^2.15.1

  '@parcel/transformer-json@2.15.1':
    resolution: {integrity: sha512-UCL8RSn+BTT2j6vEKvQ022s5zNzSPwkFFVUY26zMcrcx6MQXKZMGa5AGAJqfzM5s2kYBx8kTx3CmCwMbwfcWEw==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/transformer-node@2.15.1':
    resolution: {integrity: sha512-tnzEJyt3BfRmIikAPxNeXgjcuQwkY9gJuIa0dhHm1PlCPwQEAA8Kg0xNS8TiXCubFNIrVs3eAB23IzRjiQQ2iQ==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/transformer-postcss@2.15.1':
    resolution: {integrity: sha512-lFkE8mOWJP3uWAcMUB2I7gjCNdmSnRSZQEgjFE1b26WbmJPTi95IIKrgu1dw6EOhvitRkuHHPWaSb/ekdFbTdA==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/transformer-posthtml@2.15.1':
    resolution: {integrity: sha512-zgWq1sx4Brzzk87bYqkbOSZSNM58UKsyNr1t7JfQtXdTkswumtDysDvx/88bqTyDkuNRP+Iowmch9Ui3q9AbTw==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/transformer-raw@2.15.1':
    resolution: {integrity: sha512-2fh6ud2lD/RxYdhhKo8tbMBOWLfTP2w5RtijBdLRkJ/3dF7Wu+xejhYu9ks3iWA4RQxcxg7Gj1EADFBgyffKFg==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/transformer-react-refresh-wrap@2.15.1':
    resolution: {integrity: sha512-+Q7TjbMOJ7P64S8NPaoBSrD4ixp+Qgr41SOZvEUx/5WKx7R1d7dhUyKTz/61q7kVj1OmVlSv3UpasBCAVa259A==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/transformer-svg@2.15.1':
    resolution: {integrity: sha512-nTz27tXVPYHfcMyxdOlYNOe005egEiMK7Ldx04K/GqDZi5W3/aZeofhSdqzQlYyzA/gwu7iVDbsGcOPpBtuOlw==}
    engines: {node: '>= 16.0.0', parcel: ^2.15.1}

  '@parcel/types-internal@2.15.1':
    resolution: {integrity: sha512-+dsY64R8tP77384vDaathn90w5yLGH40NTpFFYhaYgNCfA1aPCiVjI04htgEspFAyAniCsd9fZQvvtJWZ8diag==}

  '@parcel/types@2.15.1':
    resolution: {integrity: sha512-55TJE7tC8hhRPhs4Ki4H5xakGJBMTtLiew8eAQx7lKajG4tc9ohneGGTqKmojzId3YzTua5KARnoUjmH/eoC4Q==}

  '@parcel/utils@2.15.1':
    resolution: {integrity: sha512-H6v0AsKU/OKeDW0deQlZyCy5IwcKQlQBxUp0cNksPLrH+PtgWtiO+ttCJFAYhaFAve5jW9oPSefbjZILp/cplQ==}
    engines: {node: '>= 16.0.0'}

  '@parcel/watcher-android-arm64@2.5.1':
    resolution: {integrity: sha512-KF8+j9nNbUN8vzOFDpRMsaKBHZ/mcjEjMToVMJOhTozkDonQFFrRcfdLWn6yWKCmJKmdVxSgHiYvTCef4/qcBA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [android]

  '@parcel/watcher-darwin-arm64@2.5.1':
    resolution: {integrity: sha512-eAzPv5osDmZyBhou8PoF4i6RQXAfeKL9tjb3QzYuccXFMQU0ruIc/POh30ePnaOyD1UXdlKguHBmsTs53tVoPw==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [darwin]

  '@parcel/watcher-darwin-x64@2.5.1':
    resolution: {integrity: sha512-1ZXDthrnNmwv10A0/3AJNZ9JGlzrF82i3gNQcWOzd7nJ8aj+ILyW1MTxVk35Db0u91oD5Nlk9MBiujMlwmeXZg==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [darwin]

  '@parcel/watcher-freebsd-x64@2.5.1':
    resolution: {integrity: sha512-SI4eljM7Flp9yPuKi8W0ird8TI/JK6CSxju3NojVI6BjHsTyK7zxA9urjVjEKJ5MBYC+bLmMcbAWlZ+rFkLpJQ==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [freebsd]

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    resolution: {integrity: sha512-RCdZlEyTs8geyBkkcnPWvtXLY44BCeZKmGYRtSgtwwnHR4dxfHRG3gR99XdMEdQ7KeiDdasJwwvNSF5jKtDwdA==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]

  '@parcel/watcher-linux-arm-musl@2.5.1':
    resolution: {integrity: sha512-6E+m/Mm1t1yhB8X412stiKFG3XykmgdIOqhjWj+VL8oHkKABfu/gjFj8DvLrYVHSBNC+/u5PeNrujiSQ1zwd1Q==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm]
    os: [linux]

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    resolution: {integrity: sha512-LrGp+f02yU3BN9A+DGuY3v3bmnFUggAITBGriZHUREfNEzZh/GO06FF5u2kx8x+GBEUYfyTGamol4j3m9ANe8w==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    resolution: {integrity: sha512-cFOjABi92pMYRXS7AcQv9/M1YuKRw8SZniCDw0ssQb/noPkRzA+HBDkwmyOJYp5wXcsTrhxO0zq1U11cK9jsFg==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [linux]

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    resolution: {integrity: sha512-GcESn8NZySmfwlTsIur+49yDqSny2IhPeZfXunQi48DMugKeZ7uy1FX83pO0X22sHntJ4Ub+9k34XQCX+oHt2A==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]

  '@parcel/watcher-linux-x64-musl@2.5.1':
    resolution: {integrity: sha512-n0E2EQbatQ3bXhcH2D1XIAANAcTZkQICBPVaxMeaCVBtOpBZpWJuf7LwyWPSBDITb7In8mqQgJ7gH8CILCURXg==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [linux]

  '@parcel/watcher-win32-arm64@2.5.1':
    resolution: {integrity: sha512-RFzklRvmc3PkjKjry3hLF9wD7ppR4AKcWNzH7kXR7GUe0Igb3Nz8fyPwtZCSquGrhU5HhUNDr/mKBqj7tqA2Vw==}
    engines: {node: '>= 10.0.0'}
    cpu: [arm64]
    os: [win32]

  '@parcel/watcher-win32-ia32@2.5.1':
    resolution: {integrity: sha512-c2KkcVN+NJmuA7CGlaGD1qJh1cLfDnQsHjE89E60vUEMlqduHGCdCLJCID5geFVM0dOtA3ZiIO8BoEQmzQVfpQ==}
    engines: {node: '>= 10.0.0'}
    cpu: [ia32]
    os: [win32]

  '@parcel/watcher-win32-x64@2.5.1':
    resolution: {integrity: sha512-9lHBdJITeNR++EvSQVUcaZoWupyHfXe1jZvGZ06O/5MflPcuPLtEphScIBL+AiCWBO46tDSHzWyD0uDmmZqsgA==}
    engines: {node: '>= 10.0.0'}
    cpu: [x64]
    os: [win32]

  '@parcel/watcher@2.5.1':
    resolution: {integrity: sha512-dfUnCxiN9H4ap84DvD2ubjw+3vUNpstxa0TneY/Paat8a3R4uQZDLSvWjmznAY/DoahqTHl9V46HF/Zs3F29pg==}
    engines: {node: '>= 10.0.0'}

  '@parcel/workers@2.15.1':
    resolution: {integrity: sha512-WBuNTLWK2Y0ghhA8I/DwMReURS+sTat5pFbkxqTdznsrq+MVJTDnp54YzEYryLhaca8vhFZediIh159msGB6PQ==}
    engines: {node: '>= 16.0.0'}
    peerDependencies:
      '@parcel/core': ^2.15.1

  '@swc/core-darwin-arm64@1.11.24':
    resolution: {integrity: sha512-dhtVj0PC1APOF4fl5qT2neGjRLgHAAYfiVP8poJelhzhB/318bO+QCFWAiimcDoyMgpCXOhTp757gnoJJrheWA==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [darwin]

  '@swc/core-darwin-x64@1.11.24':
    resolution: {integrity: sha512-H/3cPs8uxcj2Fe3SoLlofN5JG6Ny5bl8DuZ6Yc2wr7gQFBmyBkbZEz+sPVgsID7IXuz7vTP95kMm1VL74SO5AQ==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [darwin]

  '@swc/core-linux-arm-gnueabihf@1.11.24':
    resolution: {integrity: sha512-PHJgWEpCsLo/NGj+A2lXZ2mgGjsr96ULNW3+T3Bj2KTc8XtMUkE8tmY2Da20ItZOvPNC/69KroU7edyo1Flfbw==}
    engines: {node: '>=10'}
    cpu: [arm]
    os: [linux]

  '@swc/core-linux-arm64-gnu@1.11.24':
    resolution: {integrity: sha512-C2FJb08+n5SD4CYWCTZx1uR88BN41ZieoHvI8A55hfVf2woT8+6ZiBzt74qW2g+ntZ535Jts5VwXAKdu41HpBg==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]

  '@swc/core-linux-arm64-musl@1.11.24':
    resolution: {integrity: sha512-ypXLIdszRo0re7PNNaXN0+2lD454G8l9LPK/rbfRXnhLWDBPURxzKlLlU/YGd2zP98wPcVooMmegRSNOKfvErw==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [linux]

  '@swc/core-linux-x64-gnu@1.11.24':
    resolution: {integrity: sha512-IM7d+STVZD48zxcgo69L0yYptfhaaE9cMZ+9OoMxirNafhKKXwoZuufol1+alEFKc+Wbwp+aUPe/DeWC/Lh3dg==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]

  '@swc/core-linux-x64-musl@1.11.24':
    resolution: {integrity: sha512-DZByJaMVzSfjQKKQn3cqSeqwy6lpMaQDQQ4HPlch9FWtDx/dLcpdIhxssqZXcR2rhaQVIaRQsCqwV6orSDGAGw==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [linux]

  '@swc/core-win32-arm64-msvc@1.11.24':
    resolution: {integrity: sha512-Q64Ytn23y9aVDKN5iryFi8mRgyHw3/kyjTjT4qFCa8AEb5sGUuSj//AUZ6c0J7hQKMHlg9do5Etvoe61V98/JQ==}
    engines: {node: '>=10'}
    cpu: [arm64]
    os: [win32]

  '@swc/core-win32-ia32-msvc@1.11.24':
    resolution: {integrity: sha512-9pKLIisE/Hh2vJhGIPvSoTK4uBSPxNVyXHmOrtdDot4E1FUUI74Vi8tFdlwNbaj8/vusVnb8xPXsxF1uB0VgiQ==}
    engines: {node: '>=10'}
    cpu: [ia32]
    os: [win32]

  '@swc/core-win32-x64-msvc@1.11.24':
    resolution: {integrity: sha512-sybnXtOsdB+XvzVFlBVGgRHLqp3yRpHK7CrmpuDKszhj/QhmsaZzY/GHSeALlMtLup13M0gqbcQvsTNlAHTg3w==}
    engines: {node: '>=10'}
    cpu: [x64]
    os: [win32]

  '@swc/core@1.11.24':
    resolution: {integrity: sha512-MaQEIpfcEMzx3VWWopbofKJvaraqmL6HbLlw2bFZ7qYqYw3rkhM0cQVEgyzbHtTWwCwPMFZSC2DUbhlZgrMfLg==}
    engines: {node: '>=10'}
    peerDependencies:
      '@swc/helpers': '>=0.5.17'
    peerDependenciesMeta:
      '@swc/helpers':
        optional: true

  '@swc/counter@0.1.3':
    resolution: {integrity: sha512-e2BR4lsJkkRlKZ/qCHPw9ZaSxc0MVUd7gtbtaB7aMvHeJVYe8sOB8DBZkP2DtISHGSku9sCK6T6cnY0CtXrOCQ==}

  '@swc/helpers@0.5.17':
    resolution: {integrity: sha512-5IKx/Y13RsYd+sauPb2x+U/xZikHjolzfuDgTAl/Tdf3Q8rslRvC19NKDLgAJQ6wsqADk10ntlv08nPFw/gO/A==}

  '@swc/types@0.1.21':
    resolution: {integrity: sha512-2YEtj5HJVbKivud9N4bpPBAyZhj4S2Ipe5LkUG94alTpr7in/GU/EARgPAd3BwU+YOmFVJC2+kjqhGRi3r0ZpQ==}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  axios@1.9.0:
    resolution: {integrity: sha512-re4CqKTJaURpzbLHtIi6XpDv20/CnpXOtjRY5/CU32L8gU8ek9UIivcfvSWvmKEngmVbrUtPpdDwWDWL7DNHvg==}

  base-x@3.0.11:
    resolution: {integrity: sha512-xz7wQ8xDhdyP7tQxwdteLYeFfS68tSMNCZ/Y37WJ4bhGfKPpqEIlmIyueQHqOyoPhE6xNUqjzRr8ra0eF9VRvA==}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  braces@3.0.3:
    resolution: {integrity: sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==}
    engines: {node: '>=8'}

  browserslist@4.24.5:
    resolution: {integrity: sha512-FDToo4Wo82hIdgc1CQ+NQD0hEhmpPjrZ3hiUgwgOG6IuTdlpr8jdjyG24P6cNP1yJpTLzS5OcGgSw0xmDU1/Tw==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  buffer@6.0.3:
    resolution: {integrity: sha512-FTiCpNxtwiZZHEZbcbTIcZjERVICn9yq/pDFkTl95/AxzD1naBctN7YO68riM/gLSDY7sdrMby8hofADYuuqOA==}

  call-bind-apply-helpers@1.0.2:
    resolution: {integrity: sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==}
    engines: {node: '>= 0.4'}

  caniuse-lite@1.0.30001718:
    resolution: {integrity: sha512-AflseV1ahcSunK53NfEs9gFWgOEmzr0f+kaMFA4xiLZlr9Hzt7HxcSpIFcnNCUkz6R6dWKa54rUz3HUmI3nVcw==}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  chrome-trace-event@1.0.4:
    resolution: {integrity: sha512-rNjApaLzuwaOTjCiT8lSDdGN1APCiqkChLMJxJPWLunPAt5fy8xgU9/jNOchV84wfIxrA0lRQB7oCT8jrn/wrQ==}
    engines: {node: '>=6.0'}

  clone@2.1.2:
    resolution: {integrity: sha512-3Pe/CF1Nn94hyhIYpjtiLhdCoEoz0DqQ+988E9gmeEdQZlojxnOb74wctFyuwWQHzqyf9X7C7MG8juUpqBJT8w==}
    engines: {node: '>=0.8'}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  commander@12.1.0:
    resolution: {integrity: sha512-Vw8qHK3bZM9y/P10u3Vib8o/DdkvA2OtPtZvD871QKjy74Wj1WSKFILMPRPSdUSx5RFK1arlJzEtA4PkFgnbuA==}
    engines: {node: '>=18'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  detect-libc@1.0.3:
    resolution: {integrity: sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==}
    engines: {node: '>=0.10'}
    hasBin: true

  detect-libc@2.0.4:
    resolution: {integrity: sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA==}
    engines: {node: '>=8'}

  dotenv-expand@11.0.7:
    resolution: {integrity: sha512-zIHwmZPRshsCdpMDyVsqGmgyP0yT8GAgXUnkdAoJisxvf33k7yO6OuoKmcTGuXPWSsm8Oh88nZicRLA9Y0rUeA==}
    engines: {node: '>=12'}

  dotenv@16.5.0:
    resolution: {integrity: sha512-m/C+AwOAr9/W1UOIZUo232ejMNnJAJtYQjUbHoNTBNTJSvqzzDh7vnrei3o3r3m9blf6ZoDkvcw0VmozNRFJxg==}
    engines: {node: '>=12'}

  dunder-proto@1.0.1:
    resolution: {integrity: sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==}
    engines: {node: '>= 0.4'}

  electron-to-chromium@1.5.155:
    resolution: {integrity: sha512-ps5KcGGmwL8VaeJlvlDlu4fORQpv3+GIcF5I3f9tUKUlJ/wsysh6HU8P5L1XWRYeXfA0oJd4PyM8ds8zTFf6Ng==}

  es-define-property@1.0.1:
    resolution: {integrity: sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==}
    engines: {node: '>= 0.4'}

  es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==}
    engines: {node: '>= 0.4'}

  es-object-atoms@1.1.1:
    resolution: {integrity: sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==}
    engines: {node: '>= 0.4'}

  es-set-tostringtag@2.1.0:
    resolution: {integrity: sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA==}
    engines: {node: '>= 0.4'}

  escalade@3.2.0:
    resolution: {integrity: sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==}
    engines: {node: '>=6'}

  fill-range@7.1.1:
    resolution: {integrity: sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==}
    engines: {node: '>=8'}

  follow-redirects@1.15.9:
    resolution: {integrity: sha512-gew4GsXizNgdoRyqmyfMHyAmXsZDk6mHkSxZFCzW9gwlbtOW44CDtYavM+y+72qD/Vq2l550kMF52DT8fOLJqQ==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  form-data@4.0.2:
    resolution: {integrity: sha512-hGfm/slu0ZabnNt4oaRZ6uREyfCj6P4fT/n6A1rGV+Z0VdGXjfOhVUpkn6qVQONHGIFwmveGXyDs75+nr6FM8w==}
    engines: {node: '>= 6'}

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  get-intrinsic@1.3.0:
    resolution: {integrity: sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==}
    engines: {node: '>= 0.4'}

  get-port@4.2.0:
    resolution: {integrity: sha512-/b3jarXkH8KJoOMQc3uVGHASwGLPq3gSFJ7tgJm2diza+bydJPTGOibin2steecKeOylE8oY2JERlVWkAJO6yw==}
    engines: {node: '>=6'}

  get-proto@1.0.1:
    resolution: {integrity: sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==}
    engines: {node: '>= 0.4'}

  globals@13.24.0:
    resolution: {integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==}
    engines: {node: '>=8'}

  gopd@1.2.0:
    resolution: {integrity: sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==}
    engines: {node: '>= 0.4'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-symbols@1.1.0:
    resolution: {integrity: sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==}
    engines: {node: '>= 0.4'}

  has-tostringtag@1.0.2:
    resolution: {integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==}
    engines: {node: '>= 0.4'}

  hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==}
    engines: {node: '>= 0.4'}

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  lightningcss-darwin-arm64@1.30.1:
    resolution: {integrity: sha512-c8JK7hyE65X1MHMN+Viq9n11RRC7hgin3HhYKhrMyaXflk5GVplZ60IxyoVtzILeKr+xAJwg6zK6sjTBJ0FKYQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [darwin]

  lightningcss-darwin-x64@1.30.1:
    resolution: {integrity: sha512-k1EvjakfumAQoTfcXUcHQZhSpLlkAuEkdMBsI/ivWw9hL+7FtilQc0Cy3hrx0AAQrVtQAbMI7YjCgYgvn37PzA==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [darwin]

  lightningcss-freebsd-x64@1.30.1:
    resolution: {integrity: sha512-kmW6UGCGg2PcyUE59K5r0kWfKPAVy4SltVeut+umLCFoJ53RdCUWxcRDzO1eTaxf/7Q2H7LTquFHPL5R+Gjyig==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [freebsd]

  lightningcss-linux-arm-gnueabihf@1.30.1:
    resolution: {integrity: sha512-MjxUShl1v8pit+6D/zSPq9S9dQ2NPFSQwGvxBCYaBYLPlCWuPh9/t1MRS8iUaR8i+a6w7aps+B4N0S1TYP/R+Q==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm]
    os: [linux]

  lightningcss-linux-arm64-gnu@1.30.1:
    resolution: {integrity: sha512-gB72maP8rmrKsnKYy8XUuXi/4OctJiuQjcuqWNlJQ6jZiWqtPvqFziskH3hnajfvKB27ynbVCucKSm2rkQp4Bw==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-arm64-musl@1.30.1:
    resolution: {integrity: sha512-jmUQVx4331m6LIX+0wUhBbmMX7TCfjF5FoOH6SD1CttzuYlGNVpA7QnrmLxrsub43ClTINfGSYyHe2HWeLl5CQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [linux]

  lightningcss-linux-x64-gnu@1.30.1:
    resolution: {integrity: sha512-piWx3z4wN8J8z3+O5kO74+yr6ze/dKmPnI7vLqfSqI8bccaTGY5xiSGVIJBDd5K5BHlvVLpUB3S2YCfelyJ1bw==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]

  lightningcss-linux-x64-musl@1.30.1:
    resolution: {integrity: sha512-rRomAK7eIkL+tHY0YPxbc5Dra2gXlI63HL+v1Pdi1a3sC+tJTcFrHX+E86sulgAXeI7rSzDYhPSeHHjqFhqfeQ==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [linux]

  lightningcss-win32-arm64-msvc@1.30.1:
    resolution: {integrity: sha512-mSL4rqPi4iXq5YVqzSsJgMVFENoa4nGTT/GjO2c0Yl9OuQfPsIfncvLrEW6RbbB24WtZ3xP/2CCmI3tNkNV4oA==}
    engines: {node: '>= 12.0.0'}
    cpu: [arm64]
    os: [win32]

  lightningcss-win32-x64-msvc@1.30.1:
    resolution: {integrity: sha512-PVqXh48wh4T53F/1CCu8PIPCxLzWyCnn/9T5W1Jpmdy5h9Cwd+0YQS6/LwhHXSafuc61/xg9Lv5OrCby6a++jg==}
    engines: {node: '>= 12.0.0'}
    cpu: [x64]
    os: [win32]

  lightningcss@1.30.1:
    resolution: {integrity: sha512-xi6IyHML+c9+Q3W0S4fCQJOym42pyurFiJUHEcEyHS0CeKzia4yZDEsLlqOFykxOdHpNy0NmvVO31vcSqAxJCg==}
    engines: {node: '>= 12.0.0'}

  lmdb@2.8.5:
    resolution: {integrity: sha512-9bMdFfc80S+vSldBmG3HOuLVHnxRdNTlpzR6QDnzqCQtCzGUEAGTzBKYMeIM+I/sU4oZfgbcbS7X7F65/z/oxQ==}
    hasBin: true

  math-intrinsics@1.1.0:
    resolution: {integrity: sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==}
    engines: {node: '>= 0.4'}

  micromatch@4.0.8:
    resolution: {integrity: sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  msgpackr-extract@3.0.3:
    resolution: {integrity: sha512-P0efT1C9jIdVRefqjzOQ9Xml57zpOXnIuS+csaB4MdZbTdmGDLo8XhzBG1N7aO11gKDDkJvBLULeFTo46wwreA==}
    hasBin: true

  msgpackr@1.11.4:
    resolution: {integrity: sha512-uaff7RG9VIC4jacFW9xzL3jc0iM32DNHe4jYVycBcjUePT/Klnfj7pqtWJt9khvDFizmjN2TlYniYmSS2LIaZg==}

  node-addon-api@6.1.0:
    resolution: {integrity: sha512-+eawOlIgy680F0kBzPUNFhMZGtJ1YmqM6l4+Crf4IkImjYrO/mqPwRMh352g23uIaQKFItcQ64I7KMaJxHgAVA==}

  node-addon-api@7.1.1:
    resolution: {integrity: sha512-5m3bsyrjFWE1xf7nz7YXdN4udnVtXK6/Yfgn5qnahL6bCkf2yKt4k3nuTKAtT4r3IG8JNR2ncsIMdZuAzJjHQQ==}

  node-gyp-build-optional-packages@5.1.1:
    resolution: {integrity: sha512-+P72GAjVAbTxjjwUmwjVrqrdZROD4nf8KgpBoDxqXXTiYZZt/ud60dE5yvCSr9lRO8e8yv6kgJIC0K0PfZFVQw==}
    hasBin: true

  node-gyp-build-optional-packages@5.2.2:
    resolution: {integrity: sha512-s+w+rBWnpTMwSFbaE0UXsRlg7hU4FjekKU4eyAih5T8nJuNZT1nNsskXpxmeqSK9UzkBl6UgRlnKc8hz8IEqOw==}
    hasBin: true

  node-releases@2.0.19:
    resolution: {integrity: sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==}

  nullthrows@1.1.1:
    resolution: {integrity: sha512-2vPPEi+Z7WqML2jZYddDIfy5Dqb0r2fze2zTxNNknZaFpVHU3mFB3R+DWeJWGVx0ecvttSGlJTI+WG+8Z4cDWw==}

  ordered-binary@1.5.3:
    resolution: {integrity: sha512-oGFr3T+pYdTGJ+YFEILMpS3es+GiIbs9h/XQrclBXUtd44ey7XwfsMzM31f64I1SQOawDoDr/D823kNCADI8TA==}

  parcel@2.15.1:
    resolution: {integrity: sha512-sDj8BgTLRsTLwbBnxTKiHB6R9lE0eNHz3umKZkbkVyYI9SgFJU4G5rGaJ5fClEHDYcFFSkBb6iKOk3PqPAWKxw==}
    engines: {node: '>= 16.0.0'}
    hasBin: true

  picocolors@1.1.1:
    resolution: {integrity: sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  proxy-from-env@1.1.0:
    resolution: {integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==}

  react-refresh@0.16.0:
    resolution: {integrity: sha512-FPvF2XxTSikpJxcr+bHut2H4gJ17+18Uy20D5/F+SKzFap62R3cM5wH6b8WN3LyGSYeQilLEcJcR1fjBSI2S1A==}
    engines: {node: '>=0.10.0'}

  regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  semver@7.7.2:
    resolution: {integrity: sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==}
    engines: {node: '>=10'}
    hasBin: true

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  term-size@2.2.1:
    resolution: {integrity: sha512-wK0Ri4fOGjv/XPy8SBHZChl8CM7uMc5VML7SqiQ0zG7+J5Vr+RMQDoHa2CNT6KHUnTGIXH34UDMkPzAUyapBZg==}
    engines: {node: '>=8'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  tslib@2.8.1:
    resolution: {integrity: sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==}

  type-fest@0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==}
    engines: {node: '>=10'}

  update-browserslist-db@1.1.3:
    resolution: {integrity: sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  utility-types@3.11.0:
    resolution: {integrity: sha512-6Z7Ma2aVEWisaL6TvBCy7P8rm2LQoPv6dJ7ecIaIixHcwfbJ0x7mWdbcwlIM5IGQxPZSFYeqRCqlOOeKoJYMkw==}
    engines: {node: '>= 4'}

  weak-lru-cache@1.2.2:
    resolution: {integrity: sha512-DEAoo25RfSYMuTGc9vPJzZcZullwIqRDSI9LOy+fkCJPi6hykCnfKaXTuPBDuXAUcqHXyOgFtHNp/kB2FjYHbw==}

snapshots:

  '@lezer/common@1.2.3': {}

  '@lezer/lr@1.4.2':
    dependencies:
      '@lezer/common': 1.2.3

  '@lmdb/lmdb-darwin-arm64@2.8.5':
    optional: true

  '@lmdb/lmdb-darwin-x64@2.8.5':
    optional: true

  '@lmdb/lmdb-linux-arm64@2.8.5':
    optional: true

  '@lmdb/lmdb-linux-arm@2.8.5':
    optional: true

  '@lmdb/lmdb-linux-x64@2.8.5':
    optional: true

  '@lmdb/lmdb-win32-x64@2.8.5':
    optional: true

  '@microsoft/fetch-event-source@2.0.1': {}

  '@mischnic/json-sourcemap@0.1.1':
    dependencies:
      '@lezer/common': 1.2.3
      '@lezer/lr': 1.4.2
      json5: 2.2.3

  '@msgpackr-extract/msgpackr-extract-darwin-arm64@3.0.3':
    optional: true

  '@msgpackr-extract/msgpackr-extract-darwin-x64@3.0.3':
    optional: true

  '@msgpackr-extract/msgpackr-extract-linux-arm64@3.0.3':
    optional: true

  '@msgpackr-extract/msgpackr-extract-linux-arm@3.0.3':
    optional: true

  '@msgpackr-extract/msgpackr-extract-linux-x64@3.0.3':
    optional: true

  '@msgpackr-extract/msgpackr-extract-win32-x64@3.0.3':
    optional: true

  '@parcel/bundler-default@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/graph': 3.5.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/rust': 2.15.1
      '@parcel/utils': 2.15.1
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/cache@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/core': 2.15.1(@swc/helpers@0.5.17)
      '@parcel/fs': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/logger': 2.15.1
      '@parcel/utils': 2.15.1
      lmdb: 2.8.5
    transitivePeerDependencies:
      - napi-wasm

  '@parcel/codeframe@2.15.1':
    dependencies:
      chalk: 4.1.2

  '@parcel/compressor-raw@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/config-default@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))(@swc/helpers@0.5.17)':
    dependencies:
      '@parcel/bundler-default': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/compressor-raw': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/core': 2.15.1(@swc/helpers@0.5.17)
      '@parcel/namer-default': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/optimizer-css': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/optimizer-html': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/optimizer-image': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/optimizer-svg': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/optimizer-swc': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))(@swc/helpers@0.5.17)
      '@parcel/packager-css': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/packager-html': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/packager-js': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/packager-raw': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/packager-svg': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/packager-wasm': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/reporter-dev-server': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/resolver-default': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/runtime-browser-hmr': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/runtime-js': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/runtime-rsc': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/runtime-service-worker': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/transformer-babel': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/transformer-css': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/transformer-html': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/transformer-image': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/transformer-js': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/transformer-json': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/transformer-node': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/transformer-postcss': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/transformer-posthtml': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/transformer-raw': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/transformer-react-refresh-wrap': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/transformer-svg': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
    transitivePeerDependencies:
      - '@swc/helpers'
      - napi-wasm

  '@parcel/core@2.15.1(@swc/helpers@0.5.17)':
    dependencies:
      '@mischnic/json-sourcemap': 0.1.1
      '@parcel/cache': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/diagnostic': 2.15.1
      '@parcel/events': 2.15.1
      '@parcel/feature-flags': 2.15.1
      '@parcel/fs': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/graph': 3.5.1
      '@parcel/logger': 2.15.1
      '@parcel/package-manager': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))(@swc/helpers@0.5.17)
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/profiler': 2.15.1
      '@parcel/rust': 2.15.1
      '@parcel/source-map': 2.1.1
      '@parcel/types': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
      '@parcel/workers': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      base-x: 3.0.11
      browserslist: 4.24.5
      clone: 2.1.2
      dotenv: 16.5.0
      dotenv-expand: 11.0.7
      json5: 2.2.3
      msgpackr: 1.11.4
      nullthrows: 1.1.1
      semver: 7.7.2
    transitivePeerDependencies:
      - '@swc/helpers'
      - napi-wasm

  '@parcel/diagnostic@2.15.1':
    dependencies:
      '@mischnic/json-sourcemap': 0.1.1
      nullthrows: 1.1.1

  '@parcel/error-overlay@2.15.1': {}

  '@parcel/events@2.15.1': {}

  '@parcel/feature-flags@2.15.1': {}

  '@parcel/fs@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/core': 2.15.1(@swc/helpers@0.5.17)
      '@parcel/feature-flags': 2.15.1
      '@parcel/rust': 2.15.1
      '@parcel/types-internal': 2.15.1
      '@parcel/utils': 2.15.1
      '@parcel/watcher': 2.5.1
      '@parcel/workers': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
    transitivePeerDependencies:
      - napi-wasm

  '@parcel/graph@3.5.1':
    dependencies:
      '@parcel/feature-flags': 2.15.1
      nullthrows: 1.1.1

  '@parcel/logger@2.15.1':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/events': 2.15.1

  '@parcel/markdown-ansi@2.15.1':
    dependencies:
      chalk: 4.1.2

  '@parcel/namer-default@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/node-resolver-core@3.6.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@mischnic/json-sourcemap': 0.1.1
      '@parcel/diagnostic': 2.15.1
      '@parcel/fs': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/rust': 2.15.1
      '@parcel/utils': 2.15.1
      nullthrows: 1.1.1
      semver: 7.7.2
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/optimizer-css@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/source-map': 2.1.1
      '@parcel/utils': 2.15.1
      browserslist: 4.24.5
      lightningcss: 1.30.1
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/optimizer-html@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/rust': 2.15.1
      '@parcel/utils': 2.15.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/optimizer-image@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/core': 2.15.1(@swc/helpers@0.5.17)
      '@parcel/diagnostic': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/rust': 2.15.1
      '@parcel/utils': 2.15.1
      '@parcel/workers': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
    transitivePeerDependencies:
      - napi-wasm

  '@parcel/optimizer-svg@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/rust': 2.15.1
      '@parcel/utils': 2.15.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/optimizer-swc@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))(@swc/helpers@0.5.17)':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/source-map': 2.1.1
      '@parcel/utils': 2.15.1
      '@swc/core': 1.11.24(@swc/helpers@0.5.17)
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - '@parcel/core'
      - '@swc/helpers'
      - napi-wasm

  '@parcel/package-manager@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))(@swc/helpers@0.5.17)':
    dependencies:
      '@parcel/core': 2.15.1(@swc/helpers@0.5.17)
      '@parcel/diagnostic': 2.15.1
      '@parcel/fs': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/logger': 2.15.1
      '@parcel/node-resolver-core': 3.6.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/types': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
      '@parcel/workers': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@swc/core': 1.11.24(@swc/helpers@0.5.17)
      semver: 7.7.2
    transitivePeerDependencies:
      - '@swc/helpers'
      - napi-wasm

  '@parcel/packager-css@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/source-map': 2.1.1
      '@parcel/utils': 2.15.1
      lightningcss: 1.30.1
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/packager-html@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/rust': 2.15.1
      '@parcel/types': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/packager-js@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/rust': 2.15.1
      '@parcel/source-map': 2.1.1
      '@parcel/types': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
      globals: 13.24.0
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/packager-raw@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/packager-svg@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/rust': 2.15.1
      '@parcel/types': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/packager-wasm@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/plugin@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/types': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/profiler@2.15.1':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/events': 2.15.1
      '@parcel/types-internal': 2.15.1
      chrome-trace-event: 1.0.4

  '@parcel/reporter-cli@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/types': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
      chalk: 4.1.2
      term-size: 2.2.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/reporter-dev-server@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/codeframe': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/source-map': 2.1.1
      '@parcel/utils': 2.15.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/reporter-tracer@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
      chrome-trace-event: 1.0.4
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/resolver-default@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/node-resolver-core': 3.6.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/runtime-browser-hmr@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/runtime-js@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/runtime-rsc@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/rust': 2.15.1
      '@parcel/utils': 2.15.1
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/runtime-service-worker@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/rust-darwin-arm64@2.15.1':
    optional: true

  '@parcel/rust-darwin-x64@2.15.1':
    optional: true

  '@parcel/rust-linux-arm-gnueabihf@2.15.1':
    optional: true

  '@parcel/rust-linux-arm64-gnu@2.15.1':
    optional: true

  '@parcel/rust-linux-arm64-musl@2.15.1':
    optional: true

  '@parcel/rust-linux-x64-gnu@2.15.1':
    optional: true

  '@parcel/rust-linux-x64-musl@2.15.1':
    optional: true

  '@parcel/rust-win32-x64-msvc@2.15.1':
    optional: true

  '@parcel/rust@2.15.1':
    optionalDependencies:
      '@parcel/rust-darwin-arm64': 2.15.1
      '@parcel/rust-darwin-x64': 2.15.1
      '@parcel/rust-linux-arm-gnueabihf': 2.15.1
      '@parcel/rust-linux-arm64-gnu': 2.15.1
      '@parcel/rust-linux-arm64-musl': 2.15.1
      '@parcel/rust-linux-x64-gnu': 2.15.1
      '@parcel/rust-linux-x64-musl': 2.15.1
      '@parcel/rust-win32-x64-msvc': 2.15.1

  '@parcel/source-map@2.1.1':
    dependencies:
      detect-libc: 1.0.3

  '@parcel/transformer-babel@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/source-map': 2.1.1
      '@parcel/utils': 2.15.1
      browserslist: 4.24.5
      json5: 2.2.3
      nullthrows: 1.1.1
      semver: 7.7.2
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/transformer-css@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/source-map': 2.1.1
      '@parcel/utils': 2.15.1
      browserslist: 4.24.5
      lightningcss: 1.30.1
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/transformer-html@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/rust': 2.15.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/transformer-image@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/core': 2.15.1(@swc/helpers@0.5.17)
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
      '@parcel/workers': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - napi-wasm

  '@parcel/transformer-js@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/core': 2.15.1(@swc/helpers@0.5.17)
      '@parcel/diagnostic': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/rust': 2.15.1
      '@parcel/source-map': 2.1.1
      '@parcel/utils': 2.15.1
      '@parcel/workers': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@swc/helpers': 0.5.17
      browserslist: 4.24.5
      nullthrows: 1.1.1
      regenerator-runtime: 0.14.1
      semver: 7.7.2
    transitivePeerDependencies:
      - napi-wasm

  '@parcel/transformer-json@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      json5: 2.2.3
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/transformer-node@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/transformer-postcss@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/rust': 2.15.1
      '@parcel/utils': 2.15.1
      clone: 2.1.2
      nullthrows: 1.1.1
      postcss-value-parser: 4.2.0
      semver: 7.7.2
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/transformer-posthtml@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/transformer-raw@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/transformer-react-refresh-wrap@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/error-overlay': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
      react-refresh: 0.16.0
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/transformer-svg@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/plugin': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/rust': 2.15.1
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/types-internal@2.15.1':
    dependencies:
      '@parcel/diagnostic': 2.15.1
      '@parcel/feature-flags': 2.15.1
      '@parcel/source-map': 2.1.1
      utility-types: 3.11.0

  '@parcel/types@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/types-internal': 2.15.1
      '@parcel/workers': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
    transitivePeerDependencies:
      - '@parcel/core'
      - napi-wasm

  '@parcel/utils@2.15.1':
    dependencies:
      '@parcel/codeframe': 2.15.1
      '@parcel/diagnostic': 2.15.1
      '@parcel/logger': 2.15.1
      '@parcel/markdown-ansi': 2.15.1
      '@parcel/rust': 2.15.1
      '@parcel/source-map': 2.1.1
      chalk: 4.1.2
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - napi-wasm

  '@parcel/watcher-android-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-arm64@2.5.1':
    optional: true

  '@parcel/watcher-darwin-x64@2.5.1':
    optional: true

  '@parcel/watcher-freebsd-x64@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-arm64-musl@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-glibc@2.5.1':
    optional: true

  '@parcel/watcher-linux-x64-musl@2.5.1':
    optional: true

  '@parcel/watcher-win32-arm64@2.5.1':
    optional: true

  '@parcel/watcher-win32-ia32@2.5.1':
    optional: true

  '@parcel/watcher-win32-x64@2.5.1':
    optional: true

  '@parcel/watcher@2.5.1':
    dependencies:
      detect-libc: 1.0.3
      is-glob: 4.0.3
      micromatch: 4.0.8
      node-addon-api: 7.1.1
    optionalDependencies:
      '@parcel/watcher-android-arm64': 2.5.1
      '@parcel/watcher-darwin-arm64': 2.5.1
      '@parcel/watcher-darwin-x64': 2.5.1
      '@parcel/watcher-freebsd-x64': 2.5.1
      '@parcel/watcher-linux-arm-glibc': 2.5.1
      '@parcel/watcher-linux-arm-musl': 2.5.1
      '@parcel/watcher-linux-arm64-glibc': 2.5.1
      '@parcel/watcher-linux-arm64-musl': 2.5.1
      '@parcel/watcher-linux-x64-glibc': 2.5.1
      '@parcel/watcher-linux-x64-musl': 2.5.1
      '@parcel/watcher-win32-arm64': 2.5.1
      '@parcel/watcher-win32-ia32': 2.5.1
      '@parcel/watcher-win32-x64': 2.5.1

  '@parcel/workers@2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))':
    dependencies:
      '@parcel/core': 2.15.1(@swc/helpers@0.5.17)
      '@parcel/diagnostic': 2.15.1
      '@parcel/logger': 2.15.1
      '@parcel/profiler': 2.15.1
      '@parcel/types-internal': 2.15.1
      '@parcel/utils': 2.15.1
      nullthrows: 1.1.1
    transitivePeerDependencies:
      - napi-wasm

  '@swc/core-darwin-arm64@1.11.24':
    optional: true

  '@swc/core-darwin-x64@1.11.24':
    optional: true

  '@swc/core-linux-arm-gnueabihf@1.11.24':
    optional: true

  '@swc/core-linux-arm64-gnu@1.11.24':
    optional: true

  '@swc/core-linux-arm64-musl@1.11.24':
    optional: true

  '@swc/core-linux-x64-gnu@1.11.24':
    optional: true

  '@swc/core-linux-x64-musl@1.11.24':
    optional: true

  '@swc/core-win32-arm64-msvc@1.11.24':
    optional: true

  '@swc/core-win32-ia32-msvc@1.11.24':
    optional: true

  '@swc/core-win32-x64-msvc@1.11.24':
    optional: true

  '@swc/core@1.11.24(@swc/helpers@0.5.17)':
    dependencies:
      '@swc/counter': 0.1.3
      '@swc/types': 0.1.21
    optionalDependencies:
      '@swc/core-darwin-arm64': 1.11.24
      '@swc/core-darwin-x64': 1.11.24
      '@swc/core-linux-arm-gnueabihf': 1.11.24
      '@swc/core-linux-arm64-gnu': 1.11.24
      '@swc/core-linux-arm64-musl': 1.11.24
      '@swc/core-linux-x64-gnu': 1.11.24
      '@swc/core-linux-x64-musl': 1.11.24
      '@swc/core-win32-arm64-msvc': 1.11.24
      '@swc/core-win32-ia32-msvc': 1.11.24
      '@swc/core-win32-x64-msvc': 1.11.24
      '@swc/helpers': 0.5.17

  '@swc/counter@0.1.3': {}

  '@swc/helpers@0.5.17':
    dependencies:
      tslib: 2.8.1

  '@swc/types@0.1.21':
    dependencies:
      '@swc/counter': 0.1.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  asynckit@0.4.0: {}

  axios@1.9.0:
    dependencies:
      follow-redirects: 1.15.9
      form-data: 4.0.2
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  base-x@3.0.11:
    dependencies:
      safe-buffer: 5.2.1

  base64-js@1.5.1: {}

  braces@3.0.3:
    dependencies:
      fill-range: 7.1.1

  browserslist@4.24.5:
    dependencies:
      caniuse-lite: 1.0.30001718
      electron-to-chromium: 1.5.155
      node-releases: 2.0.19
      update-browserslist-db: 1.1.3(browserslist@4.24.5)

  buffer@6.0.3:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  call-bind-apply-helpers@1.0.2:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2

  caniuse-lite@1.0.30001718: {}

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chrome-trace-event@1.0.4: {}

  clone@2.1.2: {}

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.4: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@12.1.0: {}

  delayed-stream@1.0.0: {}

  detect-libc@1.0.3: {}

  detect-libc@2.0.4: {}

  dotenv-expand@11.0.7:
    dependencies:
      dotenv: 16.5.0

  dotenv@16.5.0: {}

  dunder-proto@1.0.1:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-errors: 1.3.0
      gopd: 1.2.0

  electron-to-chromium@1.5.155: {}

  es-define-property@1.0.1: {}

  es-errors@1.3.0: {}

  es-object-atoms@1.1.1:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.1.0:
    dependencies:
      es-errors: 1.3.0
      get-intrinsic: 1.3.0
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  escalade@3.2.0: {}

  fill-range@7.1.1:
    dependencies:
      to-regex-range: 5.0.1

  follow-redirects@1.15.9: {}

  form-data@4.0.2:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      es-set-tostringtag: 2.1.0
      mime-types: 2.1.35

  function-bind@1.1.2: {}

  get-intrinsic@1.3.0:
    dependencies:
      call-bind-apply-helpers: 1.0.2
      es-define-property: 1.0.1
      es-errors: 1.3.0
      es-object-atoms: 1.1.1
      function-bind: 1.1.2
      get-proto: 1.0.1
      gopd: 1.2.0
      has-symbols: 1.1.0
      hasown: 2.0.2
      math-intrinsics: 1.1.0

  get-port@4.2.0: {}

  get-proto@1.0.1:
    dependencies:
      dunder-proto: 1.0.1
      es-object-atoms: 1.1.1

  globals@13.24.0:
    dependencies:
      type-fest: 0.20.2

  gopd@1.2.0: {}

  has-flag@4.0.0: {}

  has-symbols@1.1.0: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.1.0

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  ieee754@1.2.1: {}

  is-extglob@2.1.1: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-number@7.0.0: {}

  json5@2.2.3: {}

  lightningcss-darwin-arm64@1.30.1:
    optional: true

  lightningcss-darwin-x64@1.30.1:
    optional: true

  lightningcss-freebsd-x64@1.30.1:
    optional: true

  lightningcss-linux-arm-gnueabihf@1.30.1:
    optional: true

  lightningcss-linux-arm64-gnu@1.30.1:
    optional: true

  lightningcss-linux-arm64-musl@1.30.1:
    optional: true

  lightningcss-linux-x64-gnu@1.30.1:
    optional: true

  lightningcss-linux-x64-musl@1.30.1:
    optional: true

  lightningcss-win32-arm64-msvc@1.30.1:
    optional: true

  lightningcss-win32-x64-msvc@1.30.1:
    optional: true

  lightningcss@1.30.1:
    dependencies:
      detect-libc: 2.0.4
    optionalDependencies:
      lightningcss-darwin-arm64: 1.30.1
      lightningcss-darwin-x64: 1.30.1
      lightningcss-freebsd-x64: 1.30.1
      lightningcss-linux-arm-gnueabihf: 1.30.1
      lightningcss-linux-arm64-gnu: 1.30.1
      lightningcss-linux-arm64-musl: 1.30.1
      lightningcss-linux-x64-gnu: 1.30.1
      lightningcss-linux-x64-musl: 1.30.1
      lightningcss-win32-arm64-msvc: 1.30.1
      lightningcss-win32-x64-msvc: 1.30.1

  lmdb@2.8.5:
    dependencies:
      msgpackr: 1.11.4
      node-addon-api: 6.1.0
      node-gyp-build-optional-packages: 5.1.1
      ordered-binary: 1.5.3
      weak-lru-cache: 1.2.2
    optionalDependencies:
      '@lmdb/lmdb-darwin-arm64': 2.8.5
      '@lmdb/lmdb-darwin-x64': 2.8.5
      '@lmdb/lmdb-linux-arm': 2.8.5
      '@lmdb/lmdb-linux-arm64': 2.8.5
      '@lmdb/lmdb-linux-x64': 2.8.5
      '@lmdb/lmdb-win32-x64': 2.8.5

  math-intrinsics@1.1.0: {}

  micromatch@4.0.8:
    dependencies:
      braces: 3.0.3
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  msgpackr-extract@3.0.3:
    dependencies:
      node-gyp-build-optional-packages: 5.2.2
    optionalDependencies:
      '@msgpackr-extract/msgpackr-extract-darwin-arm64': 3.0.3
      '@msgpackr-extract/msgpackr-extract-darwin-x64': 3.0.3
      '@msgpackr-extract/msgpackr-extract-linux-arm': 3.0.3
      '@msgpackr-extract/msgpackr-extract-linux-arm64': 3.0.3
      '@msgpackr-extract/msgpackr-extract-linux-x64': 3.0.3
      '@msgpackr-extract/msgpackr-extract-win32-x64': 3.0.3
    optional: true

  msgpackr@1.11.4:
    optionalDependencies:
      msgpackr-extract: 3.0.3

  node-addon-api@6.1.0: {}

  node-addon-api@7.1.1: {}

  node-gyp-build-optional-packages@5.1.1:
    dependencies:
      detect-libc: 2.0.4

  node-gyp-build-optional-packages@5.2.2:
    dependencies:
      detect-libc: 2.0.4
    optional: true

  node-releases@2.0.19: {}

  nullthrows@1.1.1: {}

  ordered-binary@1.5.3: {}

  parcel@2.15.1(@swc/helpers@0.5.17):
    dependencies:
      '@parcel/config-default': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))(@swc/helpers@0.5.17)
      '@parcel/core': 2.15.1(@swc/helpers@0.5.17)
      '@parcel/diagnostic': 2.15.1
      '@parcel/events': 2.15.1
      '@parcel/feature-flags': 2.15.1
      '@parcel/fs': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/logger': 2.15.1
      '@parcel/package-manager': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))(@swc/helpers@0.5.17)
      '@parcel/reporter-cli': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/reporter-dev-server': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/reporter-tracer': 2.15.1(@parcel/core@2.15.1(@swc/helpers@0.5.17))
      '@parcel/utils': 2.15.1
      chalk: 4.1.2
      commander: 12.1.0
      get-port: 4.2.0
    transitivePeerDependencies:
      - '@swc/helpers'
      - napi-wasm

  picocolors@1.1.1: {}

  picomatch@2.3.1: {}

  postcss-value-parser@4.2.0: {}

  proxy-from-env@1.1.0: {}

  react-refresh@0.16.0: {}

  regenerator-runtime@0.14.1: {}

  safe-buffer@5.2.1: {}

  semver@7.7.2: {}

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  term-size@2.2.1: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  tslib@2.8.1: {}

  type-fest@0.20.2: {}

  update-browserslist-db@1.1.3(browserslist@4.24.5):
    dependencies:
      browserslist: 4.24.5
      escalade: 3.2.0
      picocolors: 1.1.1

  utility-types@3.11.0: {}

  weak-lru-cache@1.2.2: {}
