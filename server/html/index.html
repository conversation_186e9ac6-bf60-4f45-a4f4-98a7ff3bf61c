<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>SSE Client Demo</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>
<body class="bg-white">
    <div class="max-w-3xl mx-auto p-5">
        <h1 class="text-4xl font-bold mb-6">Sales Agent</h1>
        <div class="p-4 bg-gray-100 rounded-lg mb-6">
            <p class="text-lg mb-2">欢迎使用 Sales Agent SSE 服务</p>
            <p class="mb-4">这是一个基于 Server-Sent Events (SSE) 技术的实时数据推送服务。</p>
            <div class="flex space-x-4">
                <a class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors"
                   href="./client.html">
                    访问客户端测试页面
                </a>
                <a class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                   href="/api/sales-agent/stats" id="statsLink" target="_blank">
                    查看系统状态
                </a>
            </div>
        </div>
        <div class="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
            <h2 class="text-xl font-semibold mb-2">环境切换功能</h2>
            <p class="mb-2">系统支持在本地环境和生产环境之间切换：</p>
            <ul class="list-disc pl-5 mb-4">
                <li>本地环境（Local）：使用 127.0.0.1 作为主机地址</li>
                <li>生产环境（Production）：使用配置的生产服务器地址</li>
            </ul>
            <div class="flex items-center justify-between mb-4">
                <p>当前环境： <span class="font-medium" id="currentEnv"></span></p>
                <div class="flex items-center">
                    <span class="mr-2 text-sm font-medium text-blue-600">Local</span>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input class="sr-only peer" id="env-switch" type="checkbox">
                        <div
                            class="w-11 h-6 bg-gray-300 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-green-500 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-green-600 after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500"></div>
                    </label>
                    <span class="ml-2 text-sm font-medium text-green-600">Production</span>
                </div>
            </div>
            <p>请访问 <a class="text-blue-600 hover:underline" href="./client.html" id="clientLink">客户端测试页面</a>
                使用环境切换功能。</p>
        </div>
    </div>

    <!-- 添加系统状态对话框 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50" id="statsModal">
        <div class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <div class="flex items-center">
                    <h2 class="text-2xl font-bold mr-3">系统状态</h2>
                    <button class="p-1 text-blue-500 hover:text-blue-700 rounded-full hover:bg-blue-50"
                            id="refreshStats"
                            title="刷新状态">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                             xmlns="http://www.w3.org/2000/svg">
                            <path d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" stroke-linecap="round" stroke-linejoin="round"
                                  stroke-width="2"/>
                        </svg>
                    </button>
                </div>
                <button class="text-gray-500 hover:text-gray-700" id="closeModal">
                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                         xmlns="http://www.w3.org/2000/svg">
                        <path d="M6 18L18 6M6 6l12 12" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"/>
                    </svg>
                </button>
            </div>
            <div class="space-y-4" id="statsContent">
                <div class="animate-pulse">
                    <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div class="h-4 bg-gray-200 rounded w-5/6 mb-2"></div>
                    <div class="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 环境设置
        let isLocal = localStorage.getItem('isLocal') === 'true';
        if (isLocal === null) {
            // 默认为本地环境
            isLocal = true;
            localStorage.setItem('isLocal', 'true');
        }

        // 获取当前环境的主机地址
        function getHost() {
            return isLocal ? '127.0.0.1:3000' : 'sales-agent.inhand.online:3000';
        }

        // 初始化环境切换开关
        const envSwitch = document.getElementById('env-switch');
        const currentEnv = document.getElementById('currentEnv');

        // 设置开关初始状态
        envSwitch.checked = !isLocal;

        // 更新环境显示
        function updateEnvironmentDisplay() {
            currentEnv.textContent = isLocal ? 'Local (127.0.0.1:3000)' : 'Production (sales-agent.inhand.online:3000)';
            currentEnv.className = isLocal ? 'font-medium text-blue-600' : 'font-medium text-green-600';
        }

        // 初始化环境显示
        updateEnvironmentDisplay();

        // 监听环境切换
        envSwitch.addEventListener('change', function () {
            isLocal = !this.checked;
            localStorage.setItem('isLocal', isLocal);
            updateEnvironmentDisplay();
        });

        // 获取系统状态数据的函数
        function fetchSystemStats() {
            const statsContent = document.getElementById('statsContent');

            // 显示加载动画
            statsContent.innerHTML = `
                <div class="animate-pulse">
                    <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div class="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
                    <div class="h-4 bg-gray-200 rounded w-5/6 mb-2"></div>
                    <div class="h-4 bg-gray-200 rounded w-2/3"></div>
                </div>
            `;

            // 获取系统状态数据
            const host = getHost();
            return fetch(`http://${host}/api/sales-agent/stats`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // 格式化时间戳
                    const timestamp = new Date(data.timestamp * 1000).toLocaleString();

                    // 创建任务统计表格
                    const tasksTable = createTable(
                        ['状态', '数量'],
                        [
                            ['等待执行', data.tasks.pending || 0],
                            ['正在执行', data.tasks.running || 0],
                            ['排队中', data.tasks.queued || 0],
                            ['已完成', data.tasks.completed || 0],
                            ['失败', data.tasks.failed || 0],
                            ['总计', data.tasks.total || 0]
                        ]
                    );

                    // 创建Worker统计表格
                    const workersTable = createTable(
                        ['状态', '数量'],
                        [
                            ['活跃', data.workers.active || 0],
                            ['空闲', data.workers.idle || 0],
                            ['总计', data.workers.total || 0],
                            ['最大容量', data.workers.max_capacity || 0]
                        ]
                    );

                    // 更新对话框内容
                    statsContent.innerHTML = `
                        <div class="mb-4">
                            <p class="text-sm text-gray-500">更新时间: ${timestamp}</p>
                        </div>
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold mb-2">任务统计</h3>
                            ${tasksTable}
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold mb-2">Worker 统计</h3>
                            ${workersTable}
                        </div>
                    `;

                    return data;
                })
                .catch(error => {
                    statsContent.innerHTML = `
                        <div class="p-4 bg-red-50 text-red-600 rounded-md">
                            <p>获取系统状态失败: ${error.message}</p>
                        </div>
                    `;
                    throw error;
                });
        }

        // 处理系统状态链接点击
        document.getElementById('statsLink').addEventListener('click', function (e) {
            e.preventDefault();
            const modal = document.getElementById('statsModal');

            // 显示对话框
            modal.classList.remove('hidden');
            modal.classList.add('flex');

            // 获取系统状态数据
            fetchSystemStats();
        });

        // 处理刷新按钮点击
        document.getElementById('refreshStats').addEventListener('click', function () {
            fetchSystemStats();
        });


        // 关闭对话框
        document.getElementById('closeModal').addEventListener('click', function () {
            const modal = document.getElementById('statsModal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        });

        // 点击对话框外部关闭
        document.getElementById('statsModal').addEventListener('click', function (e) {
            if (e.target === this) {
                this.classList.add('hidden');
                this.classList.remove('flex');
            }
        });

        // 创建表格的辅助函数
        function createTable(headers, rows) {
            let tableHTML = `
                <table class="min-w-full bg-white border border-gray-200 rounded-md overflow-hidden">
                    <thead class="bg-gray-100">
                        <tr>
            `;

            // 添加表头
            headers.forEach(header => {
                tableHTML += `<th class="px-4 py-2 text-left text-gray-700">${header}</th>`;
            });

            tableHTML += `
                        </tr>
                    </thead>
                    <tbody>
            `;

            // 添加数据行
            rows.forEach(row => {
                tableHTML += `<tr class="border-t border-gray-200">`;
                row.forEach((cell, index) => {
                    if (index === 0) {
                        tableHTML += `<td class="px-4 py-2 text-gray-700">${cell}</td>`;
                    } else {
                        tableHTML += `<td class="px-4 py-2 text-gray-700 font-medium">${cell}</td>`;
                    }
                });
                tableHTML += `</tr>`;
            });

            tableHTML += `
                    </tbody>
                </table>
            `;

            return tableHTML;
        }
    </script>
</body>
</html>
