<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>SSE Client Demo</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
    <script type="module" src="./client.js"></script>
</head>
<body class="bg-white">
<div class="max-w-4xl mx-auto p-5">
        <h1 class="text-4xl font-bold mb-6">Sales Agent</h1>

        <div
            class="p-3 my-3 rounded-lg bg-stone-50 text-gray-700 flex items-center justify-between transition-all duration-300"
            id="env">
            <div>
                <span>环境: </span>
                <span id="env-value" class="font-medium"></span>
                <span id="env-host" class="text-xs text-gray-500 block"></span>
            </div>
            <div class="flex items-center">
                <span class="mr-2 text-sm font-medium text-blue-600">Local</span>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="env-switch" class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-300 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-green-500 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-green-600 after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-500"></div>
                </label>
                <span class="ml-2 text-sm font-medium text-green-600">Production</span>
            </div>
        </div>
        <div
            class="p-3 my-3 rounded-lg bg-stone-50 text-gray-700 flex items-center justify-between transition-all duration-300"
            id="query_type">
            <div>
                <span>查询类型: </span>
                <span id="query_type-value" class="font-medium"></span>
                <span id="query_type_text" class="text-xs text-gray-500 block"></span>
            </div>
            <div class="flex items-center">
                <span class="mr-2 text-sm font-medium text-blue-600">Account ID</span>
            </div>
        </div>
        <div class="flex items-center justify-between">
            <div id="status-title" class="p-3 my-3 rounded-lg text-gray-700">状态: </div>
            <div id="status" class="p-3 my-3 rounded-lg text-gray-700">准备就绪</div>
        </div>

        <form aria-label="Account Id Form" class="space-y-4 mb-4" id="account_id_form">
            <div class="space-y-2">
                <label for="account_id" class="block text-sm font-medium text-gray-700">Account ID:</label>
                <input aria-required="true" id="account_id" name="account_id" value="3091799000289213015" placeholder="请输入CRM 中 Account ID" required
                       type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent">
                <span aria-hidden="true" class="text-xs text-gray-500" id="account_id_hint">例如：3091799000289213015</span>
            </div>
             <div class="space-y-2">
                <label class="block text-sm font-medium text-gray-700" for="prompt">自定义 Prompt:</label>
                <textarea aria-label="自定义 Prompt"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                       id="prompt" name="custom_prompt"
                       placeholder="请输入自定义prompt"
                          type="text"></textarea>
            </div>
        </form>

        <div class="flex items-center justify-between gap-2">
            <button type="submit" id="startButton"
                class="w-full px-5 py-3 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors duration-200"
                    aria-label="开始处理公司信息">开始处理
            </button>
            <button type="button" id="query_result"
                class="w-full px-5 py-3 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
                    aria-label="获取结果">获取结果
            </button>
        </div>
        <div class="mt-6 p-4 border border-gray-200 rounded-lg min-h-[200px] max-h-[600px] overflow-y-auto"
             id="results"></div>
        <div id="error" class="mt-6 p-4 border border-red-200 rounded-lg min-h-[200px] overflow-y-auto hidden"></div>
        <!-- 将返回的data.data 数据展示在页面中, 使用 table 形式 -->
        <table id="data" class="mt-6 p-4 border border-gray-200 rounded-lg min-h-[200px] overflow-y-auto hidden w-full">
            <thead class="bg-gray-100">
                <tr class="text-left border-b border-gray-200">
                    <th class="p-2 border-r border-gray-200">姓名</th>
                    <th class="p-2 border-r border-gray-200">邮箱</th>
                    <th class="p-2 border-r border-gray-200">职位</th>
                    <th class="p-2">LinkedIn</th>
                </tr>
            </thead>
            <tbody id="data-body"></tbody>
        </table>
    </div>
</body>
</html>
