import asyncio
from contextlib import suppress
from typing import Annotated, Any, Callable, Dict, Union

from loguru import logger
from taskiq import AckableMessage, Context, TaskiqDepends, TaskiqMessage, TaskiqResult
from taskiq.api import run_receiver_task
from taskiq.receiver import Receiver as <PERSON><PERSON><PERSON><PERSON><PERSON>
from taskiq.serializers import MSGPackSerializer
from taskiq_redis import RedisAsyncResultBackend, RedisStreamBroker

from config import (
    MAX_CONCURRENT_TASKS,
    TASK_REDIS_BACKEND_PREFIX,
    TASK_REDIS_QUEUE_CONSUMER_GROUP,
    TASK_REDIS_QUEUE_CONSUMER_NAME,
    TASK_REDIS_QUEUE_NAME,
    TASK_REDIS_URL,
    TASK_RESULT_EXPIRE_TIME,
)
from server.account.prospecting_accounts_processor import ProspectingAccountsProcessor
from server.account.prospecting_thread_manager import prospecting_thread_manager
from server.data_processor import DataProcessor
from server.task.mongodb_client import mongodb_client
from server.task.task_cleaner import task_cleaner
from server.task.task_manager import task_context, task_manager

# Replace Redis broker with MongoDB broker
result_backend = RedisAsyncResultBackend(
    redis_url=TASK_REDIS_URL,
    prefix_str=TASK_REDIS_BACKEND_PREFIX,
    serializer=MSGPackSerializer(),
    result_ex_time=TASK_RESULT_EXPIRE_TIME,
)

broker = RedisStreamBroker(
    url=TASK_REDIS_URL,
    queue_name=TASK_REDIS_QUEUE_NAME,
    consumer_group_name=TASK_REDIS_QUEUE_CONSUMER_GROUP,
    consumer_name=TASK_REDIS_QUEUE_CONSUMER_NAME,
).with_result_backend(result_backend=result_backend)


# add task stream manager middleware
broker.add_middlewares(task_manager)


@broker.task
async def data_processor_task(
    context: Annotated[Context, TaskiqDepends()],
    **kwargs,
) -> Dict[str, Any]:
    task_id = context.message.task_id
    processor = DataProcessor(task_id=task_id)
    return await processor.ainvoke(input=kwargs)


@broker.task
async def prospecting_accounts_task(
    context: Annotated[Context, TaskiqDepends()],
    **kwargs,
) -> Dict[str, Any]:
    task_id = context.message.task_id
    processor = ProspectingAccountsProcessor(task_id=task_id)
    return await processor.ainvoke(input=kwargs)


receiver_task: asyncio.Task | None = None


class Receiver(BaseReceiver):
    tasks: set[asyncio.Task]

    def __init__(self, **kwargs) -> None:
        super().__init__(**kwargs)
        self.tasks = set()

    async def runner(self, queue: "asyncio.Queue[Union[bytes, AckableMessage]]") -> None:
        try:
            await super().runner(queue)
        finally:
            # wait for all tasks to be cancelled
            with suppress(asyncio.CancelledError):
                await asyncio.gather(*self.tasks)

            self.tasks.clear()
        logger.info("runner stopped.")

    async def callback(self, message: Union[bytes, AckableMessage], raise_err: bool = False) -> None:
        self.tasks.add(asyncio.current_task())
        try:
            await super().callback(message, raise_err)
        finally:
            self.tasks.discard(asyncio.current_task())

    async def run_task(self, target: Callable[..., Any], message: TaskiqMessage) -> TaskiqResult[Any]:
        with task_context(task_id=message.task_id):
            result = await super().run_task(target, message)
            if not result.is_err and isinstance(result.return_value, BaseException):
                result.is_err = True
                result.error = result.return_value
                result.return_value = None
            return result


async def startup():
    global receiver_task
    await broker.startup()
    receiver_task = asyncio.create_task(
        coro=run_receiver_task(
            broker=broker,
            receiver_cls=Receiver,
            max_async_tasks=MAX_CONCURRENT_TASKS,
            validate_params=True,
            max_prefetch=0,
            propagate_exceptions=True,
        )
    )
    receiver_task.add_done_callback(lambda _: logger.info("receiver task done"))

    # 启动任务清理器
    await task_cleaner.start()
    logger.info("任务清理器已启动")

    # 恢复所有未完成任务的事件流订阅
    await prospecting_thread_manager.restore_all_running_subscriptions()


async def shutdown():
    # 停止任务清理器
    await task_cleaner.stop()
    logger.info("任务清理器已停止")

    if receiver_task:
        receiver_task.cancel()

    # wait for receiver task to finish
    with suppress(asyncio.CancelledError):
        await receiver_task

    await broker.shutdown()
    # 关闭 MongoDB 连接
    mongodb_client.close()
    logger.info("MongoDB 连接已关闭")
