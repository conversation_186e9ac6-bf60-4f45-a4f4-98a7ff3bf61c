from typing import Optional

from loguru import logger
from pymongo import MongoClient
from pymongo.database import Database

from config import MONGODB_DB_NAME, MONGODB_URI


class MongoDBClient:
    """全局唯一的MongoDB客户端，使用单例模式"""

    _instance: Optional["MongoDBClient"] = None
    _client: Optional[MongoClient] = None
    _db: Optional[Database] = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def _initialize_client(self) -> None:
        """initialize MongoDB client with lazy loading"""
        if self._client is None:
            try:
                self._client = MongoClient(MONGODB_URI)
                self._db = self._client[MONGODB_DB_NAME]
                logger.info("MongoDB客户端初始化成功")
            except Exception as e:
                logger.error("MongoDB客户端初始化失败: {}", str(e), exc_info=True)
                raise

    @property
    def client(self) -> MongoClient:
        """get MongoDB client instance with lazy initialization"""
        if self._client is None:
            self._initialize_client()
        return self._client

    @property
    def db(self) -> Database:
        """get database instance with lazy initialization"""
        return self.client[MONGODB_DB_NAME]

    def close(self) -> None:
        """关闭MongoDB连接"""
        try:
            if self._client:
                self._client.close()
                self._client = None
                self._db = None
                logger.info("MongoDB客户端连接已关闭")
        except Exception as e:
            logger.error("关闭MongoDB连接失败: {}", str(e), exc_info=True)


# 创建全局实例
mongodb_client = MongoDBClient()
