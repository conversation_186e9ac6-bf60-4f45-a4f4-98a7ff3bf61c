import asyncio
from abc import ABC, abstractmethod
from typing import Any

from langchain_core.runnables.utils import Input
from langfuse import get_client
from loguru import logger

from server.common_types import StreamMessage
from server.task.task_callback_handler import task_callback
from server.task.task_manager import task_context, task_manager

# 模块专用日志记录器
module_logger = logger.bind(module_name="server.task.base_task_processor")


class BaseTaskProcessor(ABC):
    """任务处理器基类"""

    def __init__(self, task_id: str, task_name: str):
        self.task_id = task_id
        self.task_name = task_name

    async def send_thinking_message(self, message: str) -> None:
        """
        发送思考消息
        """
        await self.send_message(StreamMessage.from_thinking(message))

    async def send_message(self, message: StreamMessage) -> None:
        """
        处理消息回调

        Args:
            message: message to be sent
        """

        await task_manager.send_message(message)

    @abstractmethod
    async def _arun(self, data: dict[str, Any]) -> Any:
        """子类需要实现的具体任务逻辑"""
        raise NotImplementedError("subclasses must implement _arun  method")

    async def _do_invoke(self, input: Input) -> Any | Exception:
        try:
            with task_context(task_id=self.task_id):
                with task_callback(task_id=self.task_id):
                    output = await self._arun(input)
                    return output
        except asyncio.CancelledError:
            module_logger.info(f"task {self.task_name} cancelled by asyncio")
            # return error instead of raising exception, to avoid taskiq handle exception and print stack trace
            return Exception("cancelled by server")
        except BaseException as e:
            # 如果异常没有消息，添加一个默认消息
            if not str(e):
                if hasattr(e, "args") and e.args:
                    error_msg = f"Empty exception with args: {e.args}"
                else:
                    error_msg = f"Empty {type(e).__name__} exception"
                module_logger.error(
                    f"task {self.task_name} invoke error: {error_msg}. Exception type: {type(e).__name__}, Exception args: {e.args}"
                )
                # 创建一个有消息的新异常
                return type(e)(error_msg)
            else:
                module_logger.error(
                    f"task {self.task_name} invoke error: {e}. Exception type: {type(e).__name__}, Exception args: {e.args}"
                )
                return e

    async def ainvoke(self, input: Input) -> Any | Exception:
        langfuse = get_client()
        with langfuse.start_as_current_span(name=self.task_name, input=input) as span:
            span.update_trace(
                metadata={
                    "task_name": self.task_name,
                    "task_id": self.task_id,
                }
            )
            output = await self._do_invoke(input)
            if isinstance(output, Exception):
                span.update(status_message=str(output), level="ERROR")
            else:
                span.update(output=output)
            return output
