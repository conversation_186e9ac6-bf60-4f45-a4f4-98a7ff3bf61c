import asyncio
from typing import Optional

from loguru import logger

from config import TASK_CLEANUP_INTERVAL_MINUTES, TASK_TIMEOUT_MINUTES
from server.task.mongodb_store import mongodb_store


class TaskCleaner:
    """任务清理器，定期清理超时任务"""

    def __init__(
        self, timeout_minutes: int = TASK_TIMEOUT_MINUTES, cleanup_interval_minutes: int = TASK_CLEANUP_INTERVAL_MINUTES
    ):
        self.timeout_minutes = timeout_minutes
        self.cleanup_interval_minutes = cleanup_interval_minutes
        self._task: Optional[asyncio.Task] = None

    async def start(self) -> None:
        """启动清理器"""
        self._task = asyncio.create_task(self._cleanup_loop())
        logger.info(
            "任务清理器已启动，超时时间: {} 分钟，清理间隔: {} 分钟",
            self.timeout_minutes,
            self.cleanup_interval_minutes,
        )

    async def stop(self) -> None:
        """停止清理器"""
        if self._task and not self._task.done():
            self._task.cancel()
        logger.info("任务清理器已停止")

    async def _cleanup_loop(self) -> None:
        """清理循环"""
        try:
            await self._cleanup_timeout_tasks()
            await asyncio.sleep(self.cleanup_interval_minutes * 60)
        except asyncio.CancelledError:
            return
        except Exception as e:
            logger.error("任务清理过程中发生错误: {}", str(e), exc_info=True)
            # 发生错误后等待一段时间再继续
            await asyncio.sleep(60)

    async def _cleanup_timeout_tasks(self) -> None:
        """清理超时任务"""
        try:
            # 查找超时任务
            timeout_tasks = mongodb_store.find_timeout_tasks(self.timeout_minutes)

            if not timeout_tasks:
                logger.debug("没有发现超时任务")
                return

            for task in timeout_tasks:
                task_id = task.get("_id")
                if not task_id:
                    continue

                try:
                    # 标记任务为超时
                    mongodb_store.mark_task_as_timeout(task_id)
                    logger.info(
                        "清理超时任务: {} (状态: {}, 创建时间: {}, 更新时间: {})",
                        task_id,
                        task.get("status"),
                        task.get("created_at"),
                        task.get("updated_at"),
                    )
                except Exception as e:
                    logger.error("清理任务 {} 失败: {}", task_id, str(e))

        except Exception as e:
            logger.error("清理超时任务失败: {}", str(e), exc_info=True)


# 创建全局清理器实例
task_cleaner = TaskCleaner()
