import json
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI
from fastapi.responses import StreamingResponse
from loguru import logger
from pydantic import BaseModel
from taskiq import AsyncTaskiqTask

from research.task_manager import task_manager
from research.taskiq_app import (  # 导入 taskiq_app 中的 broker 和 contacts_agent_task
    contacts_agent_task,
    shutdown,
    startup,
)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """FastAPI 生命周期管理器，用于处理应用启动和关闭事件"""
    import sys

    # remove the default loguru formatter
    logger.remove()

    def loguru_formatter(record):
        """
        loguru formatter, add task_id before message, if task_id is in extra
        """
        message = "<level>{message}</level>"
        if record["extra"].get("task_id"):
            message = "<magenta>{extra[task_id]}</magenta> <level>{message}</level>"

        return (
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> "
            "<level>{level: <8}</level>"
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - " + message + "\n"
        )

    logger.add(
        sys.stderr,
        format=loguru_formatter,
    )

    logger.info("starting taskiq app")
    await startup()
    logger.info("taskiq app started")

    yield  # 将控制权交给 FastAPI

    # 关闭
    logger.info("shutting down taskiq app")
    await shutdown()
    logger.info("taskiq app closed")


app = FastAPI(lifespan=lifespan)


class SearchContactsRequest(BaseModel):
    """接收联系人外展请求的模型类"""

    company_name: str | None = None
    region: str | None = None
    requirements: str | None = ""


@app.post("/accounts/search-contacts")
async def add_search_contacts_task(request: SearchContactsRequest):
    """创建联系人外展任务并返回任务ID"""

    # 使用 taskiq 提交异步任务
    task: AsyncTaskiqTask[str] = await task_manager.add_search_contacts_task(
        company_name=request.company_name,
        region=request.region,
        requirements=request.requirements,
    )

    task_id = task.task_id
    logger.info(f"created search contacts task: {task_id}")

    # 返回任务ID和状态信息
    return {"status": "success", "task_id": task_id, "message": "search contacts task created"}


@app.get("/accounts/search-contacts/stream")
async def get_search_contacts_stream(query: str) -> StreamingResponse:
    return await search_contacts_stream(query)


@app.get("/tasks/{task_id}/stream")
async def get_task_stream(task_id: str):
    """获取任务流"""
    return StreamingResponse(
        content=task_event_generator(task_id),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # 禁用 Nginx 缓冲
        },
    )


@app.get("/tasks/{task_id}/progress")
async def get_task_progress(task_id: str):
    """获取任务进度"""
    task = task_manager.broker.find_task(task_id)
    if not task:
        return {"status": "error", "message": "task not found", "task_id": task_id}

    progress = await task_manager.get_progress(task_id)
    return {
        "status": progress.state,
        "message": progress.meta.get("current_step", ""),
        "task_id": task_id,
        "task_name": task.task_name,
        "labels": task.labels,
    }


async def task_event_generator(task_id: str) -> AsyncGenerator[str, None]:
    # 使用管理器的订阅方法获取消息
    try:
        async for message in task_manager.subscribe(task_id):
            yield f"data: {json.dumps(message, ensure_ascii=False)}\n\n"
            if message.get("type") in ["error", "end"]:
                break
    except Exception as e:
        yield f"data: {json.dumps({'type': 'error', 'message': str(e)}, ensure_ascii=False)}\n\n"


async def search_contacts_stream(query: str) -> StreamingResponse:
    # 使用 taskiq 提交异步任务
    task: AsyncTaskiqTask[str] = await contacts_agent_task.kiq(query)

    task_id = task.task_id
    logger.info(f"submitted search contacts task: {task_id}")

    return StreamingResponse(
        content=task_event_generator(task_id),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",  # 禁用 Nginx 缓冲
        },
    )
