<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人外展工具</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>
<body class="bg-gray-50">
    <div class="max-w-3xl mx-auto p-5">
        <h1 class="text-3xl font-bold mb-6 text-blue-700">联系人外展工具</h1>
        
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-800">查询潜在联系人</h2>
            
            <form id="outreachForm" class="space-y-4">
                <div>
                    <label for="company" class="block text-sm font-medium text-gray-700 mb-1">目标公司</label>
                    <input type="text" id="company" name="company" 
                        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="region" class="block text-sm font-medium text-gray-700 mb-1">目标地区</label>
                    <input type="text" id="region" name="region" 
                        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="requirements" class="block text-sm font-medium text-gray-700 mb-1">自定义提示词</label>
                    <textarea id="requirements" name="requirements" rows="3"
                        class="w-full px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"></textarea>
                </div>
                
                <div class="flex items-center justify-between">
                    <button type="submit" 
                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                        开始查询
                    </button>
                    
                    <button type="button" id="resetBtn"
                        class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors">
                        重置
                    </button>
                </div>
            </form>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow mb-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-semibold text-gray-800">查询结果</h2>
                <div id="status" class="hidden px-3 py-1 text-sm font-medium rounded-full"></div>
            </div>
            
            <div id="loading" class="hidden">
                <div class="flex items-center justify-center py-4">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span class="text-gray-700">正在查询中，请稍候...</span>
                </div>
            </div>
            
            <div id="result" class="bg-gray-50 rounded-md p-4 min-h-[200px] overflow-auto whitespace-pre-wrap"></div>
        </div>
    </div>
    
    <script>
        // 获取DOM元素
        const form = document.getElementById('outreachForm');
        const resetBtn = document.getElementById('resetBtn');
        const status = document.getElementById('status');
        const loading = document.getElementById('loading');
        const result = document.getElementById('result');
        
        // 重置表单和结果
        function resetForm() {
            form.reset();
            result.textContent = '';
            status.classList.add('hidden');
            loading.classList.add('hidden');
        }
        
        // 监听重置按钮点击
        resetBtn.addEventListener('click', resetForm);
        
        // 处理表单提交
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // 获取表单数据
            const formData = new FormData(form);
            const data = {
                company_name: formData.get('company'),
                region: formData.get('region'),
                requirements: formData.get('requirements')
            };
            
            // 清空之前的结果
            result.textContent = '';
            status.classList.add('hidden');
            
            // 显示加载状态
            loading.classList.remove('hidden');
            
            try {
                // 直接使用 fetch 的 stream 功能处理 SSE 响应
                const response = await fetch('/api/research/search-contacts', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                if (!response.ok) {
                    throw new Error(`请求失败: ${response.status} ${response.statusText}`);
                }
                
                // 获取响应的可读流
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                
                // 处理流数据
                while (true) {
                    const { done, value } = await reader.read();
                    
                    if (done) {
                        break;
                    }
                    
                    // 解码二进制数据
                    const chunk = decoder.decode(value, { stream: true });
                    buffer += chunk;
                    
                    // 处理 SSE 格式的数据（data: {...}\n\n）
                    let processBuffer = () => {
                        const lines = buffer.split('\n\n');
                        // 保留最后一个可能不完整的块
                        buffer = lines.pop() || '';
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                try {
                                    // 提取 JSON 数据
                                    const jsonStr = line.substring(6);
                                    const data = JSON.parse(jsonStr);
                                    
                                    // 隐藏加载状态
                                    loading.classList.add('hidden');
                                    
                                    // 处理不同类型的消息
                                    if (data.content) {
                                        result.textContent += data.content;
                                    } else if (data.message) {
                                        result.textContent += data.message;
                                    } else if (data.text) {
                                        result.textContent += data.text;
                                    }
                                    
                                    // 检查是否完成
                                    if (data.type === 'completed' || data.status === 'completed' || data.is_final === true) {
                                        showSuccess('查询完成');
                                    }
                                    
                                    // 如果发生错误
                                    if (data.type === 'error' || data.status === 'error') {
                                        showError(data.message || '查询过程中发生错误');
                                    }
                                } catch (e) {
                                    console.error('解析SSE消息失败:', e, line);
                                    // 尝试直接显示原始数据
                                    result.textContent += line + '\n';
                                }
                            }
                        }
                    };
                    
                    processBuffer();
                }
                
                // 处理完成
                if (buffer.trim() && buffer.startsWith('data: ')) {
                    try {
                        const jsonStr = buffer.substring(6);
                        const data = JSON.parse(jsonStr);
                        
                        // 处理最后一条消息
                        if (data.content) result.textContent += data.content;
                        else if (data.message) result.textContent += data.message;
                        else if (data.text) result.textContent += data.text;
                        
                        if (data.type === 'completed' || data.status === 'completed' || data.is_final === true) {
                            showSuccess('查询完成');
                        }
                    } catch (e) {
                        console.error('解析最后一条消息失败:', e);
                    }
                }
                
                // 确保加载状态隐藏
                loading.classList.add('hidden');
                
            } catch (error) {
                console.error('发生异常:', error);
                loading.classList.add('hidden');
                showError(error.message || '查询失败，请重试');
            }
        });
        
        // 显示错误信息
        function showError(message) {
            status.textContent = message;
            status.classList.remove('hidden', 'bg-green-100', 'text-green-700');
            status.classList.add('bg-red-100', 'text-red-700');
        }
        
        // 显示成功信息
        function showSuccess(message) {
            status.textContent = message;
            status.classList.remove('hidden', 'bg-red-100', 'text-red-700');
            status.classList.add('bg-green-100', 'text-green-700');
        }
    </script>
</body>
</html> 