import asyncio
from typing import Any, AsyncGenerator

from loguru import logger
from pubsub.core import Publisher
from taskiq import AsyncResultBackend, AsyncTaskiqTask, TaskiqMiddleware, TaskiqResult
from taskiq.depends.progress_tracker import TaskProgress
from taskiq.kicker import AsyncKicker
from taskiq.message import TaskiqMessage


class AsyncTask(AsyncTaskiqTask[str]):
    """
    Taskiq task with progress
    """

    latest_progress: TaskProgress[str] | None = None

    def __init__(self, task_id: str, result_backend: AsyncResultBackend[str]):
        super().__init__(task_id, result_backend=result_backend, return_type=str)

    async def get_progress(self) -> TaskProgress[str] | None:
        """get progress from result backend"""

        progress = await super().get_progress()
        self.latest_progress = progress
        return progress


class TaskStreamManager(TaskiqMiddleware):
    """
    任务消息流管理器

    支持为每个任务创建消息流，并允许多个订阅者接收同一个任务的消息。
    使用广播模式 - 每个消息会发送给所有订阅者。

    作为 TaskiqMiddleware 实现，可以自动监控任务的执行过程。
    """

    def __init__(self):
        self.publisher = Publisher()

    # TaskiqMiddleware 接口实现
    async def pre_send(self, message: TaskiqMessage) -> TaskiqMessage:
        """在任务发送前被调用"""
        return message

    async def post_send(self, message: TaskiqMessage) -> TaskiqMessage:
        """在任务发送后被调用"""
        task_id = message.task_id
        await self.set_progress(task_id, "QUEUED", "task sent to executor")

        return message

    async def pre_execute(self, message: TaskiqMessage) -> TaskiqMessage:
        """在任务执行前被调用"""
        task_id = message.task_id
        await self.set_progress(task_id, "RUNNING", "task started")
        return message

    async def post_execute(self, message: TaskiqMessage, result: TaskiqResult[Any]) -> Any:
        """在任务执行后被调用"""
        task_id = message.task_id

        status = "COMPLETED" if not result.is_err else "FAILED"
        status_message = "task completed" if not result.is_err else f"task failed: {result.error}"

        await self.set_progress(task_id, status, status_message)

        self.send_message(task_id, *self.build_task_finished_messages(result))

        # unsubscribe all subscribers for this task
        self.publisher.unsubAll(topicName=task_id)

        return result

    def build_task_finished_messages(self, result: TaskiqResult[Any]) -> list[dict[str, Any]]:
        """构建任务结束消息"""
        messages = []
        if result.is_err:
            messages.append({"type": "error", "content": result.error})
        else:
            messages.append({"type": "result", "content": result.return_value})

        messages.append({"type": "end"})
        return messages

    async def get_async_task(self, task_id: str) -> AsyncTask | None:
        """获取异步任务"""

        task = AsyncTask(task_id, self.broker.result_backend)
        progress = await task.get_progress()
        return task if progress else None

    async def subscribe(self, task_id: str) -> AsyncGenerator[dict[str, Any], None]:
        """
        订阅特定任务的消息队列

        Args:
            task_id: 任务ID

        Returns:
            AsyncGenerator: 异步生成器，生成订阅的消息
        """

        task = await self.get_async_task(task_id)
        if not task:
            raise ValueError(f"task {task_id} not found")

        queue = asyncio.Queue()

        async def check_task_progress():
            if await task.is_ready():
                result = await task.get_result()
                logger.debug(f"task {task_id} is already finished.")
                self.send_message(task_id, *self.build_task_finished_messages(result))

        def callback(message: dict[str, Any]) -> None:
            queue.put_nowait(message)

        self.publisher.subscribe(callback, task_id)

        await check_task_progress()

        try:
            # 从订阅者队列读取消息并生成
            while True:
                try:
                    # 增加超时机制，防止无限等待
                    message = await asyncio.wait_for(queue.get(), timeout=30)  # 30秒超时
                    yield message

                    # 如果是结束消息，退出循环
                    if message.get("type") == "end":
                        break
                except asyncio.TimeoutError:
                    await check_task_progress()
        finally:
            # 清理订阅者
            self.publisher.unsubscribe(callback, task_id)

    def send_message(self, task_id: str, *messages: dict[str, Any]) -> None:
        """
        向任务队列中添加消息

        Args:
            task_id: 任务ID
            messages: 要添加的消息
        """
        for message in messages:
            self.publisher.sendMessage(task_id, message=message)

    async def get_progress(self, task_id: str) -> TaskProgress:
        """
        获取特定任务的状态

        Args:
            task_id: 任务ID

        Returns:
            str: 任务状态
        """
        return await self.broker.result_backend.get_progress(task_id=task_id)

    async def set_progress(self, task_id: str, status: str, current_step: str = "") -> None:
        """
        设置特定任务的状态

        Args:
            task_id: 任务ID
        """
        await self.broker.result_backend.set_progress(
            task_id=task_id, progress=TaskProgress(state=status, meta={"current_step": current_step})
        )

        self.send_message(
            task_id,
            {"type": "status", "status": status, "message": current_step},
        )

    async def add_search_contacts_task(
        self,
        company_name: str | None = None,
        region: str | None = None,
        requirements: str | None = "",
    ) -> AsyncTaskiqTask[str]:
        """
        添加联系人搜索任务
        """

        from research.taskiq_app import contacts_agent_task

        kicker: AsyncKicker[str, str] = contacts_agent_task.kicker().with_labels(
            labels={"company_name": company_name, "region": region, "requirements": requirements}
        )
        task: AsyncTaskiqTask[str] = await kicker.kiq(company_name, region, requirements)

        return task


# 创建全局管理器实例
task_manager = TaskStreamManager()
