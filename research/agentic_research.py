"""
Agentic research module using <PERSON><PERSON><PERSON><PERSON> React Agent with Tavi<PERSON> and Firecrawl tools
"""

from typing import Any, Dict, Optional

from langchain_core.messages import HumanMessage
from langchain_core.prompts import ChatPromptTemplate
from langfuse.langchain import CallbackHandler
from langgraph.prebuilt import create_react_agent
from loguru import logger

from agent.tools import firecrawl_crawl, firecrawl_map, firecrawl_scrape, tavily_search, think
from utils.logging_callback_handler import LoggingCallbackHandler
from utils.models import init_model


class AgenticResearch:
    """
    Agentic research class using React Agent with multiple research tools
    """

    def __init__(self, model_name: str = "gpt-4.1-mini"):
        """
        Initialize the agentic research agent

        Args:
            model_name: the language model to use for research
        """
        self.model_name = model_name
        self.llm = init_model(
            model=model_name,
            max_tokens=4096,
            temperature=0.1,
        )

        # available research tools
        self.tools = [
            tavily_search,
            firecrawl_scrape,
            firecrawl_crawl,
            firecrawl_map,
            think,
        ]

        # system prompt for research agent
        self.system_prompt = """你是一名专业的AI调研助手，擅长制定执行计划并使用各种搜索和网页抓取工具进行深度调研。

你的核心原则：**始终先制定计划，再执行调研**

你的能力包括：
1. 使用 think 工具进行策略思考、计划制定和结果分析
2. 使用 tavily_search 工具进行互联网搜索，获取实时信息
3. 使用 firecrawl_scrape 工具抓取特定网页的详细内容
4. 使用 firecrawl_crawl 工具爬取网站的所有可访问页面
5. 使用 firecrawl_map 工具获取网站的页面结构图

## 调研工作流程：
1. **计划制定阶段**：使用 think 工具分析调研需求，制定最优执行计划
   - 分析调研目标和范围
   - 评估可用工具的适用性
   - 制定分步骤的执行策略
   - 预估所需资源和时间

2. **信息收集阶段**：按计划执行调研任务
   - 使用 tavily_search 进行初步信息搜索
   - 使用 firecrawl_map 了解目标网站结构
   - 使用 firecrawl_scrape 抓取关键页面内容
   - 使用 firecrawl_crawl 进行深度挖掘

3. **分析整合阶段**：处理收集到的信息
   - 使用 think 工具分析和整合信息
   - 识别关键发现和模式
   - 验证信息的准确性和相关性

4. **报告生成阶段**：输出最终调研结果
   - 结构化组织调研发现
   - 提供数据支持的洞察
   - 给出可行的建议和结论

## 工具使用指南：
- tavily_search: 适合搜索最新信息、新闻、市场动态等
- firecrawl_scrape: 适合抓取特定页面的详细内容
- firecrawl_crawl: 适合全面爬取整个网站
- firecrawl_map: 适合了解网站结构，为后续抓取做准备
- think: 适合分析、推理、制定计划

## 注意事项：
- **关键原则：必须先用 think 工具制定详细计划，再开始调研**
- 每个阶段完成后，使用 think 工具评估进展和调整策略
- 所有搜索关键词必须使用英文以获得更好的结果
- 确保提供的网页链接真实有效
- 引用信息时必须标明来源
- 提供详细、准确、有价值的调研结果
- 使用中文回复用户，但工具调用时用英文

## 输出要求：
- 提供结构化的调研报告
- 包含信息来源和引用
- 突出关键发现和洞察
- 给出可行的建议和结论
"""

        # create the react agent
        self.prompt = ChatPromptTemplate.from_messages(
            [
                ("system", self.system_prompt),
                ("placeholder", "{messages}"),
            ]
        )

        self.agent = create_react_agent(
            model=self.llm,
            tools=self.tools,
            prompt=self.prompt,
        )

        logger.info(f"Initialized AgenticResearch with {len(self.tools)} tools")

    async def rewrite_query(self, original_query: str) -> Dict[str, Any]:
        """
        Rewrite and optimize user query for better research results

        Args:
            original_query: the original user query to rewrite

        Returns:
            dict containing rewritten query and any clarification questions
        """

        # query rewriting prompt
        query_rewrite_prompt = """你是一个专业的查询重写助手，专门负责优化和改进用户的查询语句，使其更加清晰、准确和易于理解。

## 核心任务
将用户输入的查询语句重写为更优质的版本，提升查询的准确性、完整性和表达效果。

## 重写原则
1. **保持原意**：确保重写后的查询与原始意图完全一致
2. **提升清晰度**：消除歧义，使表达更加明确
3. **补充信息**：在不改变原意的前提下，适当补充有助于理解的上下文
4. **优化结构**：调整语句结构，使逻辑更加清晰
5. **规范表达**：使用标准、专业的表达方式

## 重写策略
- **语法优化**：修正语法错误，完善句式结构
- **词汇提升**：使用更准确、更专业的词汇
- **逻辑梳理**：重新组织信息层次，突出重点
- **背景补充**：在必要时添加相关背景信息
- **格式规范**：统一格式，提升可读性

## 输出格式
**重写后查询：**
```md
[优化后的查询语句, 使用markdown block]
```

## 处理范围
- 模糊不清的问题
- 语法错误的查询
- 信息不完整的请求
- 表达不准确的描述
- 结构混乱的长查询

## 注意事项
- 如果原查询已经足够清晰准确，说明无需重写
- 遇到敏感或不当内容时，礼貌地指出并建议修改方向
- 保持用户的语言风格偏好（正式/非正式）
- 对于专业术语，确保使用准确


请对以下查询进行重写和优化：

{original_query}"""  # noqa: E501

        try:
            formatted_prompt = query_rewrite_prompt.format(original_query=original_query)

            # use a separate model instance for query rewriting
            rewrite_result = await self.llm.ainvoke([HumanMessage(content=formatted_prompt)])

            # parse the result to extract rewritten query and clarifications
            result_content = rewrite_result.content

            logger.info(f"Rewrited query: {result_content}")

            # simple parsing - extract rewritten query
            if "**重写后查询：**" in result_content:
                query_part = result_content.split("**重写后查询：**")[1].strip()

                # extract query from markdown block
                if "```md" in query_part:
                    query_lines = query_part.split("```md")[1].split("```")[0].strip()
                else:
                    # if no markdown block, take content until next section or end
                    lines = query_part.split("\n")
                    query_lines = lines[0].strip()

                return {
                    "rewritten_query": query_lines,
                    "original_query": original_query,
                    "raw_output": result_content,
                }
            else:
                # fallback if parsing fails
                return {
                    "rewritten_query": original_query,
                    "original_query": original_query,
                    "raw_output": result_content,
                }

        except Exception as e:
            logger.error(f"Error during query rewriting: {e}")
            return {
                "rewritten_query": original_query,
                "original_query": original_query,
                "error": str(e),
            }

    async def research(
        self, research_topic: str, context: Optional[str] = None, max_iterations: int = 20, **kwargs
    ) -> str:
        """
        Execute research on a given topic with query rewriting

        Args:
            research_topic: the main research topic or question
            context: additional context or background information
            max_iterations: maximum number of agent iterations
            **kwargs: additional parameters

        Returns:
            comprehensive research results
        """
        logger.info(f"Starting agentic research on topic: {research_topic}")

        # step 1: query rewriting
        logger.info("Step 1: Rewriting user query for optimization")
        rewrite_result = await self.rewrite_query(research_topic)
        final_query = rewrite_result["rewritten_query"]

        logger.info(f"Original query: {research_topic}")
        logger.info(f"Rewritten query: {final_query}")

        # step 2: research execution
        logger.info("Step 2: Executing research with optimized query")

        # prepare the input message with rewritten query
        if context:
            user_message = f"""请对以下主题进行深度调研：

调研主题：{final_query}

背景信息：{context}

原始查询：{research_topic}

请使用可用的工具进行全面调研，并提供详细的调研报告。"""
        else:
            user_message = f"""请对以下主题进行深度调研：

调研主题：{final_query}

原始查询：{research_topic}

请使用可用的工具进行全面调研，并提供详细的调研报告。"""

        try:
            # execute the research using the agent
            result = await self.agent.ainvoke(
                input={"messages": [HumanMessage(content=user_message)]},
                config={
                    "callbacks": [LoggingCallbackHandler(), CallbackHandler()],
                    "recursion_limit": max_iterations,
                },
            )

            # extract the final response
            final_message = result["messages"][-1]
            research_result = final_message.content

            # add query rewrite information to the result
            if final_query != research_topic:
                rewrite_info = f"""## 查询优化信息

**原始查询：** {research_topic}

**优化后查询：** {final_query}

---

"""
                research_result = rewrite_info + research_result

            logger.info("Agentic research completed successfully")
            return research_result

        except Exception as e:
            logger.error(f"Error during agentic research: {e}")
            return f"调研过程中发生错误：{str(e)}"


# convenience function for direct usage
async def agentic_research(
    research_topic: str,
    context: Optional[str] = None,
    model_name: str = "gpt-4.1",
    max_iterations: int = 20,
    **kwargs,
) -> str:
    """
    Perform agentic research on a given topic

    Args:
        research_topic: the main research topic or question
        context: additional context or background information
        model_name: the language model to use
        max_iterations: maximum number of agent iterations
        **kwargs: additional parameters

    Returns:
        comprehensive research results
    """
    research_agent = AgenticResearch(model_name=model_name)
    return await research_agent.research(
        research_topic=research_topic, context=context, max_iterations=max_iterations, **kwargs
    )
