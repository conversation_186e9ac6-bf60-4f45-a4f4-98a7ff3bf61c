import asyncio
import traceback
from pathlib import Path

from langchain_core.messages import AIMessage
from langchain_core.outputs import ChatGeneration
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from langgraph.graph.graph import CompiledGraph
from langgraph.prebuilt import create_react_agent
from loguru import logger

from utils.logging_callback_handler import LoggingCallbackHandler
from utils.models import init_model


@tool(parse_docstring=True)
async def research_task(task_description: str, collected_information: str = "", background_context: str = "") -> str:
    """Execute a comprehensive research task using available search tools.

    This tool delegates research tasks to a specialized research agent. The agent operates independently
    and doesn't have access to previous conversation context, so all necessary information must be
    provided in the parameters.

    Task Requirements:
    - task must be specific and concrete, avoid vague descriptions
    - task scope should be manageable within 3 search or web scraping operations
    - task_description must include clear step-by-step instructions
    - focus only on the original research prompt, not additional information

    Search Strategy:
    - each query should focus on one specific topic
    - break down complex tasks into simple subtasks
    - avoid including multiple different questions in a single search
    - complete current subtask before moving to the next one

    Args:
        task_description: detailed description of the research task with specific steps to be completed, in Chinese
        collected_information: information already collected from previous research, be concise and detailed, useful for the current task, in Chinese, eg: lniks
        background_context: background information and context for the research task, in Chinese

    Returns:
        comprehensive research results
    """  # noqa: E501
    try:
        logger.info("开始执行调研任务")

        # create research agent with tavily tools
        llm = init_model(
            # model="claude-3.5-haiku",
            model="gemini-2.5-pro",
            max_tokens=8192,
            temperature=0,
            max_retries=2,
            thinking_budget=256,
            include_thoughts=True,
            # thinking_budget=0,
            # use_responses_api=True,
        )
        from agent.tools import firecrawl_map, firecrawl_scrape, tavily_crawl, tavily_search
        from agent.tools.apollo import search_organizations, search_people
        from agent.tools.linkedin import get_company_details, get_company_posts

        research_tools = [
            tavily_search,
            tavily_crawl,
            firecrawl_scrape,
            firecrawl_map,
            search_organizations,
            search_people,
            get_company_details,
            get_company_posts,
        ]

        # prepare system prompt with context
        system_prompt = f"""你是一个专业的研究助手，擅长使用各种搜索工具进行深度调研。

你的任务是：
1. 理解用户的研究需求
2. 优先使用tavily_search工具搜索相关信息，
    - 如果tavily_search工具无法获取到相关信息，则使用firecrawl_scrape工具直接抓取网页内容
3. 使用tavily_extract工具提取关键网页内容，在那之前使用firecrawl_map工具了解网站的结构
4. 使用think工具进行分析思考
5. 整合所有信息，提供详细的调研结果
6. 你必须通过工具获取足够的信息，才可以回答用户的问题，不允许自己编造信息

## 背景信息
{background_context}

## 已收集的信息
{collected_information}

注意事项：
- 搜索时使用英文关键词以获得更好的结果
- 提取重要网页的详细内容
- 分析信息的可靠性和相关性
- 总是以中文回答
- 确保信息的准确性和完整性
- 抓取网页内容时，网页的链接必须来源真实，不要使用虚假的链接
- 你只能通过工具获取到的信息进行回答，不允许自己编造信息

# Citations
Always provide a citation for the source.
Use inline citations for everything using markdown link notation with the url provided. ie. [[1]](url)]
All facts must be cited.
链接必须真实，不要使用虚假的链接

## 工具使用
- 请使用 think 工具进行多步骤思考，确保调研过程清晰可追踪。
- 使用 search_organization 工具搜索公司基本信息
- 使用 search_apollo_people 工具搜索公司员工信息，其中的 organization_ids 需要通过 search_organization 工具获取
- 使用多工具进行调研，相互补充，验证信息准确性
- 如果需要的信息在一个网页中就可以获取，应使用 firecrawl_scrape 工具抓取网页内容
- 如果需要的信息在一个网页及其相关联的多个网页中，应使用 tavily_crawl 工具爬取网页内容

## 输出要求
- 将分步骤的调研结果，合并成一个完整的调研结果，不要分步骤输出
- 输出详细的调研结果，不要仅输出结论
- 确保所有事实都有引用标注，使用 [[序号]](链接) 格式

## 请按照以下格式提供最后的调研结果：

### 1. 执行摘要
- 关键发现和结论概述
- 重要信息点总结

### 2. 调研结果
- 按主题组织信息，包含引用来源 [[序号]](链接)
- 保留关键链接和简介供后续复用

### 3. 结论建议
- 基于调研的分析结论
- 行动建议和风险评估

### 4. 资源清单
- 重要信息源链接及简介
- 关键数据点摘要
- 便于后续任务复用的核心资料
"""

        # create research agent
        research_agent = create_react_agent(
            name="research_agent",
            model=llm,
            tools=research_tools,
            prompt=ChatPromptTemplate.from_messages(
                [
                    ("system", system_prompt),
                    ("placeholder", "{messages}"),
                ]
            ),
        )

        # execute research
        result = await research_agent.ainvoke(
            {"messages": [("human", task_description)]},
            config={"recursion_limit": 30},
        )

        # extract final message content
        final_message = result["messages"][-1]
        if isinstance(final_message, AIMessage):
            generation = ChatGeneration(message=final_message)
            logger.info("调研任务完成")
            return generation.text if generation.text else "调研任务执行失败，未能获取有效结果"
        else:
            logger.warning("调研结果格式异常")
            return "调研任务执行失败，未能获取有效结果"

    except Exception as e:
        logger.error(f"Error executing research task: {e} {traceback.format_exc()}")
        return f"Error executing research task: {str(e)}"


async def create_agent() -> CompiledGraph:
    """Create a React agent with tools for ISP partnership outreach."""
    llm = init_model(
        # model=model_name,
        # model="gpt-4.1",
        model="gemini-2.5-pro",
        max_tokens=8192,
        temperature=0.1,
        thinking_budget=128,
        include_thoughts=True,
        max_retries=2,
    )

    # import firecrawl and os at the start

    # 创建基本工具列表
    from agent.tools import tavily_search
    from agent.tools.apollo import search_organizations

    tools = [
        search_organizations,
        # think,
        research_task,
        tavily_search,
        # tavily_extract,
        # firecrawl_scrape,
        # firecrawl_crawl,
        # firecrawl_map,
    ]

    # add firecrawl tools to the tools list
    # tools.extend([firecrawl_scrape, firecrawl_crawl, firecrawl_map])

    logger.info(f"loaded {len(tools)} tools")

    with open(Path(__file__).parent / "research.md", "r") as f:
        system_prompt = f.read()

    prompt = ChatPromptTemplate.from_messages(
        [
            ("system", system_prompt),
            ("placeholder", "{messages}"),
        ]
    )

    agent = create_react_agent(
        # llm.bind_tools(tools, parallel_tool_calls=True) if model_name.startswith("gpt") else llm,
        model=llm,
        tools=tools,
        prompt=prompt,
    )
    return agent


async def main():
    from dotenv import load_dotenv
    from langfuse.langchain import CallbackHandler

    load_dotenv()
    load_dotenv(".env.local", override=True)

    try:
        # 初始化Langfuse回调
        langfuse_handler = CallbackHandler()

        # Create agent with tools
        agent_executor = await create_agent()

        # Example query focused on ISP partnerships
        # 我有一个成功案例：
        # InHand Networks 为工业发电机提供预测性维护解决方案，结合实时监控、智能数据分析和远程管理，提升可靠性、降低成本、
        # 提高运营效率。
        # 该方案满足发电机制造商在减少计划外停机和维护成本的同时，提升客户满意度和设备安全的迫切需求。
        # 主要功能包括对发电机参数的实时监测、智能数据分析以提供可操作的洞察，以及远程管理能力。
        # 部署采用 EC312 边缘计算机和物联网技术，监测温度、振动等参数，确保及时发现异常并优化维护计划。
        # 我们的目标是：向有这个解决方案需求的发电机制造商提供我们的 EC312 边缘计算机。

        query = """
作为映翰通SDR，帮我调研这公司，他是 reseller 的角色，看是否可以把他发展为我们的 reseller，让他们也可以卖我们的路由器，网关，边缘计算机，车载路由器之类的产品。
调研公司: https://thetechnologydoctors.co.uk/
地区：英国

## 分析经验：
- 如果客户已经在用某家的同类硬件产品了，说明他们已经有类似的成熟方案，这种情况下我们攻克的可能性就较低了
- 分析公司的员工分布情况，看他们是不是真正的本土公司
- 分析公司合作伙伴列表，看下是不是有我们（映翰通）的竞品公司，如果有说明公司跟我们的目标客户画像非常相似，攻克的可能性就较高
- 如果客户已经使用相关的硬件产品，找出来使用的谁家的。

## 预期调研报告结构：
1. 公司概况
- 基本信息：公司名称、行业、联系方式（电话、邮箱）、地址、公司规模、商业规模、公司类型（集成商、解决方案提供商等）。
- 主营业务、产品线、产品定位
- 目标市场和客户群体、独特卖点
- 公司发展阶段
- 新闻动态、媒体报道、博客文章，识别公司的重要里程碑、产品发布、融资事件、市场扩张等。

2. 技术匹配度分析
- 是否需要我们想要卖的产品
- 是否已有相关硬件产品
- 现有的硬件产品是什么，供应商是谁
- 与我们想要卖的产品的匹配程度，是否可以替代
- 潜在应用场景

3. SDR 行动建议
- 客户优先级评级（高/中/低）
- 推荐接触策略（不要联系人、沟通渠道）
- 关键销售切入点

4. 风险和机会评估
- 成单可能性预判
- 需要重点关注的因素
        """  # noqa: E501

        query2 = """
I want to know companies like ingersoll rand who use edge gateways for its air compressors remote monitoring

你需要先对目标公司进行调研，构建他的画像，然后根据画像找到相似的公司。

工作流程：
- 分析模板公司的画像：公司规模、业务模式、市场定位、行业、合作伙伴等
- 相似公司识别：基于画像特征搜索同类型公司
- 对每个公司：
    - 业务匹配度分析：评估与映翰通产品的契合度
    - 合作可能性评估：综合分析攻克难度和成功概率
    - 有没有使用我们想卖的硬件产品，如果有的话使用的哪一家的？
- 综合得出最可能攻克且最相似的公司

不要以下公司：
无

输出要求：
公司名称、官网、相似度、推理理由、画像的各个维度对应的描述
"""

        # Run the agent with Langfuse回调
        result = await agent_executor.ainvoke(
            input={"messages": [{"role": "user", "content": query}]},
            config={"callbacks": [langfuse_handler, LoggingCallbackHandler()], "recursion_limit": 50},
        )

        # Log the result
        logger.info("Agent execution completed successfully")

    except Exception as e:
        logger.error(f"Error running ISP partnership outreach agent: {e}", exc_info=True)


if __name__ == "__main__":
    # print(search_isp_contacts("verizon"))
    asyncio.run(main())
