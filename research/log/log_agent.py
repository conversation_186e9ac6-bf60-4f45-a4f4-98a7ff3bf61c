import asyncio
import datetime
import functools
import re
from pathlib import Path
from typing import List

import dotenv
from langchain.tools import tool
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import chain
from langgraph.prebuilt import create_react_agent
from loguru import logger
from pydantic import BaseModel, Field

from utils.agent import print_stream_events
from utils.models import init_model

log_file_name = "syslog20231219175017.log"


class LogEntry(BaseModel):
    """log entry data structure"""

    line_number: int = Field(description="line number in the file (1-based)")
    raw_content: str = Field(description="original raw log line content")
    timestamp: str = Field(description="parsed timestamp string")
    log_level: str = Field(description="log level (info, error, warning, debug, etc.)")
    message: str = Field(description="actual log message content")
    facility_code: int = Field(default=0, description="syslog facility code")
    severity_code: int = Field(default=0, description="syslog severity code")


@functools.lru_cache(maxsize=1)
def get_log_entries() -> List[LogEntry]:
    """
    parse complete log file and extract line number, date, log level, and full content.

    Returns:
        list of LogEntry objects containing parsed log information

    Raises:
        FileNotFoundError: if log file doesn't exist
        Exception: if there's an error reading the file
    """
    log_file = Path(__file__).parent / log_file_name
    if not log_file.exists():
        raise FileNotFoundError(f"log file {log_file} not found")

    try:
        with open(log_file, "r", encoding="utf-8") as f:
            lines = f.readlines()
    except Exception as e:
        raise Exception(f"error reading log file: {e}")

    log_entries = []

    # syslog format regex patterns
    # pattern for standard syslog format: <priority>timestamp hostname process[pid]: message
    syslog_pattern = re.compile(
        r"^<(\d+)>\s*"  # priority code in angle brackets
        r"(\w+\s+\d+\s+\d+:\d+:\d+)\s*"  # timestamp (e.g., "Dec 19 17:50:17")
        r"(.*)$"  # rest of the message
    )

    for i, line in enumerate(lines, 1):
        line_content = line.rstrip("\n\r")

        # initialize log entry with defaults
        entry = LogEntry(
            line_number=i,
            raw_content=line_content,
            timestamp="",
            log_level="unknown",
            message=line_content,
            facility_code=0,
            severity_code=0,
        )

        # try to parse syslog format
        match = syslog_pattern.match(line_content)
        if match:
            priority_code = int(match.group(1))
            timestamp_str = match.group(2)
            message_part = match.group(3)

            # extract facility and severity from priority code
            # priority = facility * 8 + severity
            facility_code = priority_code >> 3
            severity_code = priority_code & 0b111

            # map severity code to log level
            severity_map = {
                0: "emergency",
                1: "alert",
                2: "critical",
                3: "error",
                4: "warning",
                5: "notice",
                6: "info",
                7: "debug",
            }

            log_level = severity_map.get(severity_code, "unknown")

            # clean up the message part
            message = message_part.strip()

            # update entry with parsed information
            entry.timestamp = timestamp_str
            entry.log_level = log_level
            entry.message = message
            entry.facility_code = facility_code
            entry.severity_code = severity_code
            log_entries.append(entry)
        else:
            logger.warning(f"unknown log format: {line_content}")

    logger.info(f"parsed {len(log_entries)} log entries from {log_file}")
    return log_entries


def parse_line_numbers(line_numbers_str: str) -> list[int]:
    """
    parse line numbers string that supports ranges like "100-105,200,300-302"

    Args:
        line_numbers_str: string containing line numbers and ranges

    Returns:
        list of individual line numbers
    """
    result = []
    parts = line_numbers_str.split(",")

    for part in parts:
        part = part.strip()
        if "-" in part:
            # handle range like "100-105"
            start, end = part.split("-", 1)
            try:
                start_num = int(start.strip())
                end_num = int(end.strip())
                result.extend(range(start_num, end_num + 1))
            except ValueError:
                continue
        else:
            # handle single number
            try:
                result.append(int(part))
            except ValueError:
                continue

    return sorted(list(set(result)))


@tool(parse_docstring=True)
async def get_log_content(
    start_time: str = "",
    end_time: str = "",
    start_line: int = 0,
    end_line: int = 0,
    pattern: str = "",
    log_level: int = 7,
) -> str:
    """
    search log content from syslog file with time range or line number filtering and pattern matching.

    Args:
        start_time: start time for filtering logs (format: MM-DD HH:MM:SS)
        end_time: end time for filtering logs (format: MM-DD HH:MM:SS)
        start_line: start line number for filtering logs (1-based)
        end_line: end line number for filtering logs (1-based)
        pattern: search pattern to match in log lines, use english
        log_level: log level for filtering logs (linux syslog priority code, 0-7 for emergency to debug),
            only return log entries less than or equal to the log level. default to 7 (debug).

    Returns:
        filtered log content with matching lines
    """
    log_entries = get_log_entries()

    # prepare filtering parameters
    use_line_filter = start_line > 0 or end_line > 0
    use_time_filter = start_time and end_time

    start_datetime = None
    end_datetime = None

    if use_time_filter:
        try:
            start_datetime = datetime.datetime.strptime(start_time, "%m-%d %H:%M:%S")
            end_datetime = datetime.datetime.strptime(end_time, "%m-%d %H:%M:%S")
        except ValueError as e:
            return f"error parsing time format: {e}. expected format: MM-DD HH:MM:SS"

    # compile pattern regex if provided
    pattern_regex = None
    if pattern:
        pattern_regex = re.compile(pattern, re.IGNORECASE)

    # filter log entries based on criteria
    filtered_results = []
    for entry in log_entries:
        # apply line number filter
        if use_line_filter:
            if start_line > 0 and entry.line_number < start_line:
                continue
            if end_line > 0 and entry.line_number > end_line:
                continue

        # apply time filter
        if use_time_filter and entry.timestamp:
            try:
                # parse log timestamp (format: "Dec 19 17:50:17")
                log_time = datetime.datetime.strptime(entry.timestamp, "%b %d %H:%M:%S")
                if not (start_datetime <= log_time <= end_datetime):
                    continue
            except ValueError:
                # if timestamp parsing fails, include the line
                pass

        # apply pattern filter
        if pattern_regex and not pattern_regex.search(entry.raw_content):
            continue

        # apply log level filter
        if entry.severity_code > log_level:
            continue

        # entry passed all filters
        filtered_results.append(f"{entry.line_number:6d}: {entry.raw_content}")

    if not filtered_results:
        filter_desc = []
        if use_line_filter:
            filter_desc.append(f"line range {start_line}-{end_line}")
        if use_time_filter:
            filter_desc.append(f"time range {start_time} to {end_time}")
        if pattern:
            filter_desc.append(f"pattern '{pattern}'")

        return f"no matches found for {', '.join(filter_desc) if filter_desc else 'specified criteria'}"

    # limit results to maximum 1000 lines
    total_lines = len(filtered_results)
    if total_lines > 20:
        head_lines = filtered_results[:10]
        tail_lines = filtered_results[-10:]
        logger.info(f"filtered {total_lines} lines (truncated to 20)")
        return (
            "\n".join(head_lines)
            + "\n...\n"
            + "\n".join(tail_lines)
            + f"\n\n[注意: 结果已截断至前10行和后10行，总共匹配了{total_lines}行]\n\n"
        )
    else:
        logger.info(f"filtered {total_lines} lines")
        return "\n".join(filtered_results)


# @tool(parse_docstring=True)
@chain
async def find_relevant_log_lines(question: str) -> str:
    """
    find all log lines that are potentially relevant to the given question using multi-round search strategy.

    Args:
        question: the user's question about the log issue

    Returns:
        string containing line numbers and ranges in format like "100-105,200,300-302"
    """
    if not question or question.strip() == "":
        return ""

    # create search agent with get_log_content tool
    search_llm = init_model(model="gpt-4.1-mini", max_tokens=2048, temperature=0.1)

    log_entries = get_log_entries()
    search_system_prompt = f"""
你是映翰通网络设备的专业日志搜索专家。你的任务是通过多轮搜索策略，找出与用户问题相关的所有日志行号。
你只需要对日志进行初步搜索，确认问题相关的日志已经锁定在3000行以内，即可完成任务。

## 搜索策略：
1. **关键词搜索**：根据问题类型使用相关关键词进行全局搜索
2. **时间范围搜索**：如果发现特定时间点的问题，搜索该时间段的日志
3. **上下文搜索**：在发现问题的行号附近搜索相关上下文
4. 日志必须是跟用户的问题相关的，不要只是关键词匹配
5. **缩小日志内容**：对于重复内容，只保留第一次出现的日志，如：反复拨号，反复重启等
6. 你只需要锁定大致的行号范围，不需要锁定具体的行号

## 时间范围搜索：
- 如果问题中包含时间，则优先搜索该时间范围内的日志
- 如果问题中没有时间，则先大致浏览日志的开头和结尾，了解日志的大致时间范围和日志格式

## 上下文分析：
1. 提取出相关的日志条目后，不要孤立地分析它们。要查看这些条目前后一段时间的日志，以获取完整的事件序列。
2. 注意事件发生的顺序，以及不同日志条目之间的关联性。例如，一个"连接断开"的错误可能后面跟着"重新连接尝试"的日志。
3. 查找重复出现的模式或频繁发生的事件，这可能指向一个持续性问题。

## 搜索流程：
1. 先用关键词进行全局搜索，识别相关事件的时间点和行号
2. 基于发现的信息，进行精细化搜索
3. 收集所有相关的行号
4. 最后返回格式化的行号范围

## 行号范围
1. 如果行号比较零散，可适当放宽范围，让行号更连续
2. 限制行号范围在3000行以内

## 日志摘要
```
{[f"{entry.line_number}: {entry.raw_content}" for entry in log_entries[:2]]}
...
{[f"{entry.line_number}: {entry.raw_content}" for entry in log_entries[-2:]]}
```

## 日志格式
```
line_number: <priority>timestamp process[pid]: message
```

## 常见进程
- redial: 拨号进程
- NetworkManager: 路由器设备管理平台的接入进程
- ih_if: inhand interface manager
- ih_record: inhand data collector and recorder, collecting wan status and other data

**重要：最终必须返回具体的行号或行号范围，格式如：100-105,200,300-302**
"""

    search_prompt = ChatPromptTemplate.from_messages(
        [
            ("system", search_system_prompt),
            ("user", f"请帮我找出与以下问题相关的所有日志行号：{question}"),
            ("placeholder", "{messages}"),
        ]
    )

    class SearchResponse(BaseModel):
        line_numbers: str = Field(description="行号范围，格式如：100-105,200,300-302")

    search_agent = create_react_agent(
        search_llm,
        tools=[get_log_content],
        prompt=search_prompt,
        response_format=SearchResponse,
    )

    try:
        # run search agent
        result = await search_agent.ainvoke({"messages": [("user", f"分析问题：{question}")]})

        # extract line numbers from agent's response
        line_numbers = result.get("structured_response").line_numbers

        if line_numbers:
            # return the most comprehensive pattern found
            # numbers = parse_line_numbers(line_numbers)
            return {"line_numbers": line_numbers, "question": question}
        else:
            logger.warning(f"no line numbers found in search result for question: {question}")
            return ""

    except Exception as e:
        logger.error(f"error during log line search: {e}")
        return ""


async def get_log_lines_content(line_numbers: str) -> str:
    """
    retrieve the actual log content for specified line numbers and ranges.

    Args:
        line_numbers: string containing line numbers and ranges like "100-105,200,300-302"

    Returns:
        formatted log content with line numbers
    """
    if not line_numbers or line_numbers.strip() == "":
        raise Exception("line_numbers is empty")

    # parse line numbers
    line_nums = parse_line_numbers(line_numbers)

    if not line_nums:
        raise Exception("no valid line numbers provided")

    # use get_log_entries to get all parsed log entries
    log_entries = get_log_entries()

    # retrieve content for specified line numbers
    result_lines = []

    for entry in log_entries:
        if entry.line_number in line_nums:
            result_lines.append(f"{entry.line_number:6d}: {entry.raw_content}")

    logger.info(f"retrieved content for {len(result_lines)} log lines")
    return "\n".join(result_lines)


@chain
async def analyze_log_errors(input_data: dict) -> str:
    """
    analyze log content for specified line numbers to identify errors and their root causes using gemini model.

    Args:
        input_data: dictionary containing line_numbers and question

    Returns:
        detailed analysis of errors found in the log content
    """
    # extract data from input
    line_numbers = input_data.get("line_numbers", "")
    question = input_data.get("question", "")

    if not line_numbers:
        return "没有提供行号"

    # get log content for the specified line numbers
    try:
        log_content = await get_log_lines_content(line_numbers)
        logger.debug(f"log_content: \n{log_content}")
    except Exception as e:
        return f"无法获取日志内容: {e}"

    # create o3 model instance for analysis
    analysis_llm = init_model(
        model="gemini-2.5-pro",
        max_tokens=4096,
        thinking_budget=512,
        # reasoning_effort="low",
        include_thoughts=True,
    )

    context_info = f"\n\n用户原始问题: {question}" if question else ""

    analysis_prompt = f"""
你是映翰通网络设备的专业日志分析专家。请仔细分析以下日志内容，识别错误原因和问题根源。{context_info}

## 分析要求：
1. 识别所有错误、警告和异常事件
2. 分析错误的时间序列和因果关系
3. 推断问题的根本原因
4. 提供具体的诊断结论和建议
5. 如果发现是蜂窝网络问题，应分析具体是什么原因导致的

## 重点关注：
- 拨号连接失败的原因
- 网络连接中断事件
- 设备重启或异常
- AT命令执行失败
- 时间同步问题
- 配置错误
- SIM卡注册问题
- 网络信号问题

## 分析经验
- Port0 是蜂窝模块

## 输出格式：
### 错误摘要
[简要描述发现的主要问题，只与用户问题相关的，不要描述与用户问题无关的]

### 详细分析
[按时间顺序分析关键事件和错误]

### 根因分析
[推断问题的根本原因]

### 解决建议
[提供具体的解决方案或建议]

请基于以下日志内容进行分析：

```
{log_content}
```
"""

    try:
        response = await analysis_llm.ainvoke(analysis_prompt)
        return response.content
    except Exception as e:
        logger.error(f"error during log analysis: {e}")
        return f"日志分析过程中发生错误: {e}"


if __name__ == "__main__":
    dotenv.load_dotenv()
    dotenv.load_dotenv(".env.local", override=True)

    from langfuse.langchain import CallbackHandler

    langfuse_handler = CallbackHandler()

    async def main():
        global log_file_name
        log_file_name = "设备重启.log"

        agent = find_relevant_log_lines | analyze_log_errors

        astream = agent.astream_events(
            input="分析我的网络故障",
            config={"recursion_limit": 100, "callbacks": [langfuse_handler]},
        )
        await print_stream_events(astream)

    asyncio.run(main())
