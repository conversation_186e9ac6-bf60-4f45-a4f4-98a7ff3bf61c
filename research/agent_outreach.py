import asyncio
import logging
from typing import cast

from dotenv import load_dotenv
from langchain_core.messages import AIMessageChunk, BaseMessage, ToolMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langfuse.langchain import Callback<PERSON>and<PERSON>
from langgraph.graph.graph import CompiledGraph
from langgraph.prebuilt import create_react_agent

from agent.tools import think

# from mcp_use.adapters import LangChainAdapter
# mcp-use imports
# from mcp_use.client import MCPClient
from research.apollo_api import search_organization
from research.contacts_agent import ContactsAgent

load_dotenv()
load_dotenv(".env.local", override=True)

logger = logging.getLogger(__name__)

# 初始化Langfuse回调
langfuse_handler = CallbackHandler()

contacts_agent = ContactsAgent()


@tool
async def search_contacts(user_query: str, organization_id: str, organization_info: str) -> str:
    """
    Get target organization's contacts
    Args:
        user_query: The sales scenario, business context, and search objectives as detailed as possible
        organization_id: target organization id
        organization_info: organization_info: Information about the target organization
    Returns:
        target organization's contacts
    """
    return await contacts_agent.ainvoke(user_query, organization_info, organization_id)


async def create_agent(model_name: str = "gpt-4.1") -> CompiledGraph:
    """Create a React agent with tools for ISP partnership outreach."""
    # 为LLM添加langfuse回调
    llm = ChatOpenAI(model=model_name, temperature=0.2, max_tokens=2048)

    from agent.tools import tavily_search

    # 创建基本工具列表
    tools = [search_organization, think, search_contacts, tavily_search]

    logger.info(f"loaded {len(tools)} tools")

    prompt = ChatPromptTemplate.from_messages(
        [
            (
                "system",
                """你是一个智能且具有丰富经验的映翰通（InHand Networks）的销售助手。
你的目标是结合工具，根据用户提供的要求，识别出目标公司中适合作为潜在客户的联系人。
以便我们可以把我的某一个产品卖给目标公司。
你需要从LinkedIn信息中识别合适的联系人，并分析他们作为潜在客户的价值。

## 映翰通
映翰通是一家专注于工业物联网（IIoT）通信与边缘计算的高科技企业。​公司致力于为工业、能源、交通、零售等行业提供"云+端"一体化的物联网解决方案，助力客户实现数字化转型和智能化升级。
映翰通的主要产品和服务包括：​
- 工业通信设备：​如工业级路由器、蜂窝网关、以太网交换机、无线数据终端（DTU）等，广泛应用于工业自动化、能源管理等领域。​
- 边缘计算平台：​提供边缘计算网关和AI加速计算机，支持本地数据处理和智能分析，提升系统响应速度和安全性。​
- 云管理平台：​如DeviceLive和InConnect，支持设备远程管理、数据可视化和智能运维。​
- 行业解决方案：​涵盖智能配电网、数字化工厂、智能售货系统、车载通信等多个领域，提供定制化的物联网应用方案。​

## 联系人搜索工作流程
1. 理解用户需求：仔细分析用户提供的销售场景、业务背景和搜索目标
2. 思考如何达成用户需求：分析目标销售场景，理解我们需要寻找什么样的联系人以及为什么他们适合我们的产品
3. 制定搜索策略：确定搜索关键词、目标职位、行业和地区等筛选条件
4. 评估搜索结果的相关性：确保搜索结果与用户需求相符
5. 搜索目标公司并筛选合适的目标公司：
   - 有多家同名公司时，需要根据用户提供的要求，筛选出合适的目标公司，并明确说明你选的依据和结果
   - 确保目标公司与用户描述的一致
   - 输出公司名称时，需要包含公司名称、公司官网、公司总部地址、LinkedIn公司主页URL等信息
6. 对每个公司，使用工具获取该公司的潜在联系人：
   - 使用search_apollo_people工具搜索公司内的联系人
   - 根据职位、资历等条件筛选合适的联系人
   - 如果结果分页，需要获取所有页面的结果
7. 评估联系人的匹配度：根据联系人的职位、工作经历、技能和个人简介，评估其作为潜在客户的价值
8. 如果用户没有明确要求某个公司，并且这个公司没有合适的联系人，则应再搜索其他公司
9. 你不能仅依靠search_organization工具获取信息，必须使用tavily-search工具搜索补充信息，如：公司详细信息、行业动态等

## 搜索企业工作流程
1. 明确搜索目标：根据用户需求确定目标企业的行业、规模、地区等特征
2. 使用tavily-search工具深入了解参考公司：获取参考公司的详细信息，包括业务模式、产品线、目标市场等
3. 准备搜索关键词：基于参考公司分析，将搜索条件转换为英文关键词，确保准确表达搜索意图
4. 使用search_organization工具获取企业目录：根据关键词搜索获取潜在匹配企业列表
5. 使用tavily-search工具深入了解每个候选企业：获取更详细的业务信息、市场定位、技术方向等
6. 筛选最合适的目标企业：选择最符合用户需求的企业作为最终目标

你必须严格按照上述流程进行，不得跳过任何步骤。

## 使用思考工具
请使用think工具进行多步骤思考。每一轮思考都必须使用think工具，确保思考过程清晰可追踪。

## 注意事项
- 搜索时如果发现还有更多页结果，应继续获取以确保不遗漏高价值联系人
- 评估联系人时，要综合考虑其职位、工作经历、技能和个人简介
- 我们期望根据你搜索的结果，通过 linkedin 与他们取得联系，所以你需要评估他们是否在 linkedin 上有个人资料或者活跃在 linkedin 上
- 使用中文回复用户
- tavily-search 的搜索条件必须使用英文

## 输出格式
输出合适的联系人，按推荐优先级排序。包含以下信息：联系人姓名、职位、公司、位置、linkedin 个人资料 URL、推荐理由、推荐信心等
""",  # noqa: E501
            ),
            ("placeholder", "{messages}"),
        ]
    )

    agent = create_react_agent(llm, tools, prompt=prompt)

    return agent


async def print_stream(stream):
    async for stream_mode, chunk in stream:
        if stream_mode == "values":
            message = chunk["messages"][-1]
            if isinstance(message, tuple):
                print(message)
            else:
                if not isinstance(message, ToolMessage):
                    cast(BaseMessage, message).pretty_print()
                elif message.status == "error" or message.name.startswith("tavily"):
                    cast(BaseMessage, message).pretty_print()
                else:
                    # message.pretty_print()
                    pass
        else:
            print(f"{stream_mode}: {chunk}")


async def main():
    """Initialize and run the ISP partnership outreach agent."""
    logger.info("Initializing ISP partnership outreach agent")

    try:
        # Create agent with tools
        agent_executor = await create_agent()

        # Example query focused on ISP partnerships
        query = """
        目标公司：Radix IoT
        目标地区：不限
        解决方案：尝试把我们的网关类设备卖给他们
        """

        # query = """
        # 目标公司：与 Blue Pillar 相似，在同一区域，做相同产品，具有同样项目机会的其它公司
        # 客户可能会有用到我们网关类设备的机会，期望找到合适的人
        # 找10个这样的公司输出他们的详情信息，考虑同一国家，做类似产品，同一行业，同一项目机会，同样的公司规模.
        # 其特点在于：专门做 iot 的，小型一点的公司，我们好接触一些。
        # 不要查找联系人，仅查询公司
        # 最后的结果必须使用 tavily-search 工具搜索相关企业的信息进行验证
        #         """

        # Run the agent with Langfuse回调
        stream = agent_executor.astream_events(
            input={"messages": [{"role": "user", "content": query}]},
            # stream_mode=["values", "custom"],
            config={"callbacks": [langfuse_handler], "recursion_limit": 50},
            include_types=["chat_model", "chain", "tool", "custom", "thinking"],
        )

        async for event in stream:
            match event["event"]:
                case "on_chat_model_stream":
                    chunk = cast(AIMessageChunk, event["data"]["chunk"])
                    if chunk.content:
                        print(chunk.content, end="", flush=True)
                    elif chunk.tool_call_chunks:
                        pass
                    else:
                        print("\n")
                case "on_chat_model_end":
                    pass
                    # print("on_chat_model_end", type(event["data"]["output"]))
                    # output = cast(ToolMessage, event["data"]["output"])
                    # print(f"{output.pretty_repr()}")
                case "on_tool_start":
                    match event["name"]:
                        case "think":
                            print(event["data"]["input"]["thought"])
                        case _:
                            print(f"on_tool_start: {event['name']}({event['data']['input']})")
                    pass
                case (
                    "on_chain_start"
                    | "on_chain_stream"
                    | "on_chain_end"
                    | "on_chat_model_start"
                    | "on_tool_start"
                    | "on_tool_end"
                ):
                    print
                    pass
                case "on_custom_event":
                    # name = event["name"]
                    # data = event["data"]
                    # print(f"{name}: {data}")
                    for item in event["data"]:
                        print(f"{item}")
                    pass
                case _:
                    print(f"{event}")
        # await print_stream(result)

        # Log the result
        logger.info("Agent execution completed successfully")
        # logger.info(f"Result: {result['output'][:100]}...")

        # Save the results to a file
        # with open("research/isp_partnership_contacts.json", "w") as f:
        #     f.write(result["output"])

    except Exception as e:
        logger.error(f"Error running ISP partnership outreach agent: {e}")
        # print stack trace
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    # print(search_isp_contacts("verizon"))
    asyncio.run(main())
