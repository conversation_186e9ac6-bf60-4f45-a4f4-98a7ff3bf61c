"""
Multi-step research agent using LangGraph, adapted into a class structure.
"""

import operator
from dataclasses import dataclass, field
from datetime import datetime
from typing import Annotated, List, TypedDict

from dotenv import load_dotenv
from langchain_core.messages import AIMessage, BaseMessage, HumanMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, START, StateGraph
from langgraph.graph.message import add_messages
from langgraph.types import Send
from loguru import logger
from pydantic import BaseModel, Field

from agent.tools.tavily import TavilySearchResponse, summary_tavily_search_results, tavily_search
from utils.logging_callback_handler import LoggingCallbackHandler
from utils.models import init_model

# load environment variables
load_dotenv()
load_dotenv(".env.local", override=True)


class Query(TypedDict):
    query: str
    rationale: str


class QueryGenerationState(TypedDict):
    query_list: list[str]


# -- Schemas for structured output --
class SearchQueryList(BaseModel):
    """a list of search queries."""

    rationale: str = Field(description="brief explanation of why these queries are relevant")
    query: List[str] = Field(description="a list of search queries, in English")


class Reflection(BaseModel):
    """reflection on the gathered information."""

    is_sufficient: bool = Field(
        description="whether the provided summaries are sufficient to answer the user's question"
    )
    knowledge_gap: str = Field(description="description of what information is missing or needs clarification")
    follow_up_queries: List[str] = Field(description="specific questions to address the knowledge gap, in English")


# -- Configuration --
class Configuration(BaseModel):
    """configuration for the research agent."""

    query_generator_model: str = "gpt-4.1-mini"
    reasoning_model: str = "gemini-2.5-pro"
    number_of_initial_queries: int = 5
    max_research_loops: int = 3

    @classmethod
    def from_runnable_config(cls, config: RunnableConfig):
        """create a configuration from a runnable config."""
        return cls(**config.get("configurable", {}))


# -- State --
class Source(TypedDict):
    """a single source with its url and id."""

    url: str
    title: str
    content: str
    score: float | None


class WebSearchState(TypedDict, total=False):
    """state for the web search node."""

    search_query: str
    id: int
    messages: list[BaseMessage]
    research_topic: str


class OverallState(TypedDict, total=False):
    """the overall state of the agent."""

    messages: Annotated[list[BaseMessage], add_messages]
    search_query: Annotated[list[str], operator.add]
    web_research_result: Annotated[list[str], operator.add]
    sources_gathered: Annotated[list[Source], operator.add]
    initial_search_query_count: int
    max_research_loops: int
    research_loop_count: int
    reasoning_model: str
    is_sufficient: bool
    knowledge_gap: str
    follow_up_queries: list[str]
    number_of_ran_queries: int
    id: int


# -- Prompts --
def get_current_date():
    """get the current date in a readable format."""
    return datetime.now().strftime("%B %d, %Y")


query_writer_instructions = """Your goal is to generate sophisticated and diverse web search queries. These queries are intended for an advanced automated web research tool capable of analyzing complex results, following links, and synthesizing information.

Instructions:
- Always prefer a single search query, only add another query if the original question requests multiple aspects or elements and one query is not enough.
- Each query should focus on one specific aspect of the original question.
- Don't produce more than {number_queries} queries.
- Queries should be diverse, if the topic is broad, generate more than 1 query.
- Don't generate multiple similar queries, 1 is enough.
- Query should ensure that the most current information is gathered. The current date is {current_date}.
- Respond in Chinese.
- Query should be in English.

Format:
- Format your response as a JSON object with ALL three of these exact keys:
   - "rationale": Brief explanation of why these queries are relevant
   - "query": A list of search queries

Example:

Topic: What revenue grew more last year apple stock or the number of people buying an iphone
```json
{{
    "rationale": "To answer this comparative growth question accurately, we need specific data points on Apple's stock performance and iPhone sales metrics. These queries target the precise financial information needed: company revenue trends, product-specific unit sales figures, and stock price movement over the same fiscal period for direct comparison.",
    "query": ["Apple total revenue growth fiscal year 2024", "iPhone unit sales growth fiscal year 2024", "Apple stock price growth fiscal year 2024"]
}}
```

Context: {research_topic}"""  # noqa: E501

web_searcher_instructions = """Conduct targeted Google Searches to gather the most recent, credible information on "{research_topic}" and synthesize it into a verifiable text artifact.

Instructions:
- Query should ensure that the most current information is gathered. The current date is {current_date}.
- Conduct multiple, diverse searches to gather comprehensive information.
- Consolidate key findings while meticulously tracking the source(s) for each specific piece of information.
- The output should be a well-written summary or report based on your search findings.
- Only include the information found in the search results, don't make up any information.
- Respond in Chinese.

Research Topic:
{research_topic}
"""  # noqa: E501

reflection_instructions = """You are an expert research assistant analyzing summaries about "{research_topic}".

Instructions:
- Identify knowledge gaps or areas that need deeper exploration and generate a follow-up query. (1 or multiple).
- If provided summaries are sufficient to answer the user's question, don't generate a follow-up query.
- If there is a knowledge gap, generate a follow-up query that would help expand your understanding.
- Focus on technical details, implementation specifics, or emerging trends that weren't fully covered.
- Respond in Chinese.

Requirements:
- Ensure the follow-up query is self-contained and includes necessary context for web search.

Output Format:
- Format your response as a JSON object with these exact keys:
   - "is_sufficient": true or false
   - "knowledge_gap": Describe what information is missing or needs clarification
   - "follow_up_queries": Write a specific question to address this gap, Must be in English.

Example:
```json
{{
    "is_sufficient": true, // or false
    "knowledge_gap": "The summary lacks information about performance metrics and benchmarks", // "" if is_sufficient is true
    "follow_up_queries": ["What are typical performance benchmarks and metrics used to evaluate [specific technology]?"] // [] if is_sufficient is true
}}
```

Reflect carefully on the Summaries to identify knowledge gaps and produce a follow-up query. Then, produce your output following this JSON format:

Summaries:
{summaries}
"""  # noqa: E501

answer_instructions = """Generate a high-quality answer to the user's question based on the provided summaries.

Instructions:
- The current date is {current_date}.
- You are the final step of a multi-step research process, don't mention that you are the final step.
- You have access to all the information gathered from the previous steps.
- You have access to the user's question.
- Generate a high-quality answer to the user's question based on the provided summaries and the user's question.
- you MUST include all the citations from the summaries in the answer correctly.
- Respond in Chinese.

User Context:
- {research_topic}

Summaries:
{summaries}"""


# -- Utils --
def get_research_topic(messages: list[BaseMessage]) -> str:
    """extract the research topic from the messages."""
    if not messages:
        return ""
    if isinstance(messages[-1], BaseMessage):
        return messages[-1].content
    return messages[-1].get("content", "")


def resolve_urls(grounding_chunks, query_id) -> dict[str, Source]:
    """resolve grounding chunks to a dictionary of sources."""
    resolved_urls = {}
    if not grounding_chunks:
        return resolved_urls
    for idx, chunk in enumerate(grounding_chunks):
        short_url = f"[{idx + 1}]"
        resolved_urls[short_url] = {"value": chunk.web.uri, "id": idx + 1, "query_id": query_id, "short_url": short_url}
    return resolved_urls


def get_citations(response, resolved_urls):
    """get citations from the response."""
    citations = []
    if (
        hasattr(response.candidates[0], "citation_metadata")
        and response.candidates[0].citation_metadata
        and response.candidates[0].citation_metadata.citation_sources
    ):
        for citation in response.candidates[0].citation_metadata.citation_sources:
            segments = []
            if hasattr(citation, "segments"):
                for segment in citation.segments:
                    if segment in resolved_urls:
                        segments.append(resolved_urls[segment])
            citations.append(
                {"start_index": citation.start_index, "end_index": citation.end_index, "segments": segments}
            )
    return citations


def insert_citation_markers(text, citations):
    """insert citation markers into the text."""
    modified_text = ""
    last_index = 0
    for citation in sorted(citations, key=lambda x: x["start_index"]):
        modified_text += text[last_index : citation["start_index"]]
        citation_markers = "".join([f"[{c['id']}]" for c in citation["segments"]])
        modified_text += citation_markers
        last_index = citation["end_index"]
    modified_text += text[last_index:]
    return modified_text


# -- Nodes --
def generate_query(state: OverallState, config: RunnableConfig) -> QueryGenerationState:
    """generate search queries based on the user's question."""
    logger.info("generating search queries")
    configurable = Configuration.from_runnable_config(config)

    if state.get("initial_search_query_count") is None:
        state["initial_search_query_count"] = configurable.number_of_initial_queries

    llm = init_model(
        model=configurable.query_generator_model,
        temperature=0.5,
    )
    structured_llm = llm.with_structured_output(SearchQueryList)

    formatted_prompt = query_writer_instructions.format(
        current_date=get_current_date(),
        research_topic=get_research_topic(state["messages"]),
        number_queries=state["initial_search_query_count"],
    )
    result = structured_llm.invoke(formatted_prompt)
    return {"query_list": result.query}


async def web_research(state: WebSearchState) -> OverallState:
    """perform web research using the google search api."""
    search_query = state["search_query"]
    logger.info(f"performing web research for query: {search_query}")

    # uses the google genai client as the langchain client doesn't return grounding metadata
    response: TavilySearchResponse | str = await tavily_search.ainvoke(
        input={"query": search_query, "include_raw_content": True}
    )
    if isinstance(response, str):
        logger.error(f"Error in tavily_search tool: {response}")
        return {"sources_gathered": [], "search_query": [search_query], "web_research_result": [response]}
    # logger.info(f"web research response: {response}")
    results = response.get("results", [])
    tavily_answer = response.get("answer", "")
    answer = await summary_tavily_search_results(search_query, response)
    logger.info(f"tavily answer: {tavily_answer}")
    logger.info(f"regenerated answer: {answer}")

    # convert results to Source type
    sources = [
        Source(url=result["url"], title=result["title"], content=result["content"], score=result["score"])
        for result in results
    ]

    return {
        "sources_gathered": sources,
        "search_query": [search_query],
        "web_research_result": [answer],
    }


def reflection(state: OverallState, config: RunnableConfig):
    """reflect on the gathered information and decide if more research is needed."""
    logger.info("reflecting on the research")
    configurable = Configuration.from_runnable_config(config)
    state["research_loop_count"] = state.get("research_loop_count", 0) + 1

    formatted_prompt = reflection_instructions.format(
        current_date=get_current_date(),
        research_topic=get_research_topic(state["messages"]),
        summaries="\n\n---\n\n".join(state["web_research_result"]),
    )
    llm = init_model(
        model=configurable.query_generator_model,
        temperature=0,
    )
    result: Reflection = llm.with_structured_output(Reflection).invoke(formatted_prompt)

    return {
        "is_sufficient": result.is_sufficient,
        "knowledge_gap": result.knowledge_gap,
        "follow_up_queries": result.follow_up_queries,
        "research_loop_count": state["research_loop_count"],
        "number_of_ran_queries": len(state.get("search_query", [])),
    }


def finalize_answer(state: OverallState, config: RunnableConfig):
    """generate the final answer."""
    logger.info("finalizing the answer")
    configurable = Configuration.from_runnable_config(config)

    formatted_prompt = answer_instructions.format(
        current_date=get_current_date(),
        research_topic=get_research_topic(state["messages"]),
        summaries="\n---\n\n".join(state["web_research_result"]),
    )

    llm = init_model(
        model=configurable.reasoning_model,
        temperature=0,
    )
    result = llm.invoke(formatted_prompt)

    # replace short urls with original urls
    # unique_sources = []
    # for source in state.get("sources_gathered", []):
    #     if source["url"] in result.content:
    #         result.content = result.content.replace(source["url"], source["value"])
    #         unique_sources.append(source)

    return {
        "messages": [AIMessage(content=result.content)],
        # "sources_gathered": unique_sources,
    }


# -- Edges --
def continue_to_web_research(state: QueryGenerationState) -> List[Send]:
    """conditional edge to start web research."""
    query_list = state.get("query_list", [])
    logger.info(f"dispatching web research tasks {query_list}")
    return [
        Send("web_research", {"search_query": search_query, "id": int(idx)})
        for idx, search_query in enumerate(query_list)
    ]


def evaluate_research(state: OverallState, config: RunnableConfig):
    """conditional edge to evaluate the research and decide the next step."""
    logger.info("evaluating research")
    configurable = Configuration.from_runnable_config(config)
    max_research_loops = (
        state.get("max_research_loops")
        if state.get("max_research_loops") is not None
        else configurable.max_research_loops
    )
    if state.get("is_sufficient"):
        logger.info("research is sufficient, finalizing answer")
        return "finalize_answer"
    elif state.get("research_loop_count", 0) >= max_research_loops:
        logger.info("research is running out of loops, finalizing answer")
        return "finalize_answer"
    else:
        logger.info("research is not sufficient, performing more web research")
        return [
            Send(
                "web_research",
                {
                    "search_query": follow_up_query,
                    "id": state.get("number_of_ran_queries", 0) + int(idx),
                    "messages": state["messages"],
                },
            )
            for idx, follow_up_query in enumerate(state.get("follow_up_queries", []))
        ]


@dataclass
class TavilyResearch:
    """Multi-step agent for account research, using a predefined graph."""

    config: Configuration = field(default_factory=Configuration)

    def __post_init__(self):
        """initializes the agent by building the graph."""
        self.graph = self._build_graph()

    def _build_graph(self):
        """build and compile the langgraph workflow."""
        builder = StateGraph(OverallState, config_schema=Configuration)

        # define the nodes we will cycle between
        builder.add_node("generate_query", generate_query)
        builder.add_node("web_research", web_research)
        builder.add_node("reflection", reflection)
        builder.add_node("finalize_answer", finalize_answer)

        # set the entrypoint
        builder.add_edge(START, "generate_query")

        # add edges
        builder.add_conditional_edges("generate_query", continue_to_web_research, ["web_research"])
        builder.add_edge("web_research", "reflection")
        builder.add_conditional_edges("reflection", evaluate_research, ["web_research", "finalize_answer"])
        builder.add_edge("finalize_answer", END)

        return builder.compile(name="tavily-research")

    async def research(self, research_topic: str) -> str:
        """research the given topic."""
        config: RunnableConfig = {
            "configurable": self.config.model_dump(),
            "callbacks": [LoggingCallbackHandler()],
        }
        state = {"messages": [HumanMessage(content=research_topic)]}
        result = await self.graph.ainvoke(state, config=config)
        return result["messages"][-1].content
