"""
direct react agent implementation with fixed tools using langgraph
"""

from typing import Sequence, Union

from langchain_core.language_models import BaseChatModel
from langchain_core.messages import (
    AIMessage,
    BaseMessage,
    SystemMessage,
    ToolMessage,
)
from langchain_core.runnables import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>bda
from langchain_core.tools import tool
from langgraph.graph import END, StateGraph
from langgraph.graph.message import add_messages
from langgraph.managed import IsLastStep, RemainingSteps
from langgraph.prebuilt.tool_node import ToolNode
from langgraph.types import Send
from typing_extensions import Annotated, TypedDict

from utils.agent import print_stream_events
from utils.models import init_model


# define agent state
class AgentState(TypedDict):
    """the state of the agent"""

    messages: Annotated[list[BaseMessage], add_messages]
    is_last_step: IsLastStep
    remaining_steps: RemainingSteps


# define fixed tools for the agent
@tool
def search_company_info(company_name: str) -> str:
    """search for company information and details"""
    return f"company info for {company_name}: a technology company with 1000+ employees"


@tool
def get_contact_info(person_name: str, company: str) -> str:
    """get contact information for a person at a company"""
    return f"contact info for {person_name} at {company}: <EMAIL>, linkedin profile available"


@tool
def analyze_market_data(industry: str) -> str:
    """analyze market data and trends for an industry"""
    return f"market analysis for {industry}: growing market with 15% annual growth rate"


class ReactAgent:
    """react agent with fixed tools for account research"""

    def __init__(self):
        # initialize model
        self.model: BaseChatModel = init_model(
            model="gemini-2.5-pro",
            include_thoughts=True,
            thinking_budget=128,
            temperature=0,
        )

        # define fixed tools
        self.tools = [search_company_info, get_contact_info, analyze_market_data]

        # bind tools to model
        self.model = self.model.bind_tools(self.tools)

        # setup tool node
        self.tool_node = ToolNode(self.tools)

        # system prompt
        self.system_prompt = """you are an account research agent. your job is to help research companies, contacts, and market information.

use the available tools to:
- search for company information
- find contact details
- analyze market data

always provide comprehensive and accurate information."""  # noqa: E501

        # check tools that return directly
        self.should_return_direct = {t.name for t in self.tools if t.return_direct}

        # create model chain
        self.model_chain = self._create_model_chain()

    def _validate_chat_history(self, messages: Sequence[BaseMessage]) -> None:
        """validate that all tool calls have corresponding tool messages"""
        all_tool_calls = [
            tool_call for message in messages if isinstance(message, AIMessage) for tool_call in message.tool_calls
        ]
        tool_call_ids_with_results = {message.tool_call_id for message in messages if isinstance(message, ToolMessage)}
        tool_calls_without_results = [
            tool_call for tool_call in all_tool_calls if tool_call["id"] not in tool_call_ids_with_results
        ]
        if tool_calls_without_results:
            raise ValueError(
                f"found AI messages with tool_calls that do not have corresponding tool messages: "
                f"{tool_calls_without_results[:3]}"
            )

    def _prepare_messages(self, state: AgentState) -> list[BaseMessage]:
        """prepare messages with validation and system prompt"""
        messages = state["messages"]
        if not messages:
            raise ValueError("expected state to have 'messages' key with content")

        self._validate_chat_history(messages)

        # prepare messages with system prompt
        system_message = SystemMessage(content=self.system_prompt)
        return [system_message] + list(messages)

    def _format_response(self, response: AIMessage) -> AgentState:
        """format model response into agent state"""
        return {"messages": [response]}

    def _create_model_chain(self) -> Runnable:
        """create a langchain runnable chain for model calling"""
        # step 1: prepare messages
        prepare_messages = RunnableLambda(self._prepare_messages, name="prepare_messages")

        # step 2: call model (supports both sync and async)
        call_model = self.model

        # step 3: format response
        format_response = RunnableLambda(self._format_response, name="format_response")

        # chain them together
        return prepare_messages | call_model | format_response

    def should_continue(self, state: AgentState) -> Union[str, list[Send]]:
        """determine whether to continue with tools or end"""
        messages = state["messages"]
        last_message = messages[-1]

        # if no tool calls, finish
        if not isinstance(last_message, AIMessage) or not last_message.tool_calls:
            return END

        # otherwise continue with tools using parallel execution
        tool_calls = [self.tool_node.inject_tool_args(call, state, None) for call in last_message.tool_calls]
        return [Send("tools", [tool_call]) for tool_call in tool_calls]

    def route_tool_responses(self, state: AgentState) -> str:
        """route tool responses back to agent or end if return_direct"""
        messages = state["messages"]

        # check for return_direct tools
        for m in reversed(messages):
            if not isinstance(m, ToolMessage):
                break
            if m.name in self.should_return_direct:
                return END

        # handle parallel tool calls case
        if isinstance(m, AIMessage) and m.tool_calls:
            if any(call["name"] in self.should_return_direct for call in m.tool_calls):
                return END

        return "agent"

    def build_graph(self):
        """build and compile the langgraph workflow"""
        # create workflow
        workflow = StateGraph(AgentState)

        # add nodes - now using the model chain directly
        workflow.add_node("agent", self.model_chain)
        workflow.add_node("tools", self.tool_node)

        # set entry point
        workflow.set_entry_point("agent")

        # add conditional edges from agent
        workflow.add_conditional_edges(
            "agent",
            self.should_continue,
            path_map=["tools", END],
        )

        # add conditional edges from tools
        if self.should_return_direct:
            workflow.add_conditional_edges("tools", self.route_tool_responses, path_map=["agent", END])
        else:
            workflow.add_edge("tools", "agent")

        # compile the workflow
        return workflow.compile(name="react_agent")


# example usage
if __name__ == "__main__":
    import asyncio

    from dotenv import load_dotenv
    from langfuse.langchain import CallbackHandler

    load_dotenv()
    load_dotenv(".env.local", override=True)

    async def main():
        langfuse_handler = CallbackHandler()
        # create the agent instance
        agent = ReactAgent()
        compiled_agent = agent.build_graph()

        # test the agent
        inputs = {
            "messages": [
                {"role": "user", "content": "research microsoft corporation and find contact info for satya nadella"}
            ]
        }

        stream = compiled_agent.astream_events(inputs, stream_mode="messages", config={"callbacks": [langfuse_handler]})
        await print_stream_events(stream)

    asyncio.run(main())
