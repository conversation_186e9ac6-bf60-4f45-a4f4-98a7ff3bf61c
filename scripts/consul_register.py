import consul
from loguru import logger

# 配置日志


class ConsulService:
    def __init__(
        self,
        host="127.0.0.1",
        port=8500,
        service_name="sales-agent",
        service_id="sales-agent-1",
        service_host="sales-agent.poweris.svc.cluster.local",
        service_port=3000,
    ):
        # 初始化 Consul 客户端
        self.c = consul.Consul(host=host, port=port)
        self.service_name = service_name
        self.service_id = service_id
        self.service_port = service_port
        self.service_host = service_host
        self.host = host  # the IP address where the service is running
        self.is_registered = False

    def register(self):
        """Register the service with <PERSON>."""
        try:
            self.c.agent.service.register(
                name=self.service_name,
                service_id=self.service_id,
                address=self.service_host,
                port=self.service_port,
                tags=[
                    "traefik.http.routers.sales-agent-webhook.rule=PathPrefix(`/api/sales-agent-webhook/`)",
                    "traefik.enable=true",
                    "traefik.http.routers.sales-agent.rule=PathPrefix(`/api/sales-agent/`)",
                    "traefik.http.routers.sales-agent.middlewares=nezha@internal",
                    "traefik.http.routers.sales-agent.metadata.nezha.scopes=plm:sales-agent:get,plm:sales-agent:post",
                ],
            )
            self.is_registered = True
            logger.info(f"Service '{self.service_name}' with ID '{self.service_id}' registered successfully.")
        except Exception as e:
            logger.error(f"Failed to register service: {e}")
            self.is_registered = False

    def deregister(self):
        """Deregister the service from Consul."""
        if self.is_registered:
            try:
                self.c.agent.service.deregister(self.service_id)
                self.is_registered = False
                logger.info(f"Service '{self.service_name}' with ID '{self.service_id}' deregistered successfully.")
            except Exception as e:
                logger.error(f"Failed to deregister service: {e}")


if __name__ == "__main__":
    service_host = "sales-agent.poweris.svc.cluster.local"  # change to your actual service IP
    service_port = 3000  # change to your actual service port
    consul_host = "127.0.0.1"  # Consul agent host
    consul_port = 8500  # Consul agent port

    # 创建服务实例
    service = ConsulService(
        host=consul_host,
        port=consul_port,
        service_name="sales-agent",
        service_id="sales-agent-1",
        service_host=service_host,
        service_port=service_port,
    )

    # 注册服务
    try:
        service.deregister()
        service.register()
    except Exception as e:
        logger.error(f"Failed to register service: {e}")
