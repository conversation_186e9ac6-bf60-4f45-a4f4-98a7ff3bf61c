import uuid
from urllib.parse import urlparse

import httpx

from config import APOLLO_API_KEY, APOLLO_BASE_URL, MAX_KEEPALIVE_CONNECTIONS, SERVER_DOMAIN
from utils.logger import get_logger

logging = get_logger(__name__)

# 设置 Apollo 最大获取职员数
APOLLO_MAX_PEOPLE_COUNT = 2000
PER_PAGE = 100  # 每页获取职员数

# 创建全局异步客户端
apollo_client = httpx.Client(
    timeout=30.0,
    headers={"X-Api-Key": APOLLO_API_KEY},
    base_url=APOLLO_BASE_URL,
    limits=httpx.Limits(max_keepalive_connections=MAX_KEEPALIVE_CONNECTIONS),
)


def get_url_hostname(url: str) -> str | None:
    """
    获取 URL 的主机名部分

    Args:
        url: 完整的 URL 字符串

    Returns:
        str: 主机名部分
    """
    if url:
        return urlparse(url).hostname
    return ""


def search_organization(
    q_organization_name: str,
    organization_locations: list[str] = None,
    organization_website: str = "",
) -> dict | list[dict]:
    """
    使用 Apollo API 搜索企业信息

    Args:
        q_organization_name: 企业名称
        organization_locations: 企业所在地区
        organization_website: 企业官网

    Returns:
        dict: 企业信息
    """
    try:
        query_params = {
            "q_organization_name": q_organization_name,
            "organization_locations[]": organization_locations,
            "per_page": 20,
            "page": 1,
        }
        response = apollo_client.post("/mixed_companies/search", params=query_params)

        if response.status_code == 200:
            result = response.json()
            all_organizations = result.get("organizations", [])
            if organization_website:
                all_organizations = [
                    org
                    for org in all_organizations
                    if org.get("website_url")
                    and get_url_hostname(org["website_url"])
                    and get_url_hostname(org["website_url"]) == get_url_hostname(organization_website)
                ]

            return all_organizations
        else:
            logging.error(f"Apollo API 搜索企业失败 - 状态码: {response.status_code}, 响应: {response.text}")
            return {"error": f"Error: {response.status_code}, {response.text}"}
    except Exception as e:
        logging.error(f"使用 Apollo API 搜索企业时出错: {str(e)}")
        return {"error": f"Error: {str(e)}"}


def search_people(organization_id: str, locations: list[str] = []) -> list[dict]:
    """
    使用 Apollo API 搜索指定企业下的 职员列表（暂最多支持2000人）

    Args:
        organization_id: 企业ID
        locations: 职员所在位置

    Returns:
        list[dict]: 职员列表
    """
    results = []
    try:
        page = 1
        total_pages = 1
        while page <= total_pages:
            if page > APOLLO_MAX_PEOPLE_COUNT / PER_PAGE:
                break  # 限制最多查询人数
            query_params = {
                "organization_ids[]": [organization_id],
                "person_locations[]": locations,
                "per_page": PER_PAGE,
                "page": page,
            }
            response = apollo_client.post("/mixed_people/search", params=query_params)

            if response.status_code == 200:
                result = response.json()
                # 获取总页数
                total_pages = result.get("pagination", {}).get("total_pages", 0)
                page += 1

                # 处理结果
                people = result.get("people", [])
                for person in people:
                    simplified_person = {
                        "id": person.get("id", ""),
                        "name": person.get("name", ""),
                        "title": person.get("title", ""),
                        "email": person.get("email", ""),
                        "linkedin_url": person.get("linkedin_url", ""),
                    }
                    if simplified_person["email"] == "<EMAIL>":
                        simplified_person["email"] = ""
                    results.append(simplified_person)
            else:
                logging.warning(
                    f"Apollo API 搜索职员列表失败 - 状态码: {response.status_code}, 响应: {response.text}"  # noqa: E501
                )
        logging.info(f"共获取到职员信息 {len(results)} 条")
    except Exception as e:
        logging.error(f"使用 Apollo API 搜索职员列表时出错: {str(e)}")

    return results


def bulk_people_enrichment(people_list: list[dict]) -> list[dict]:
    """使用 Apollo API 批量获取职员信息"""
    if people_list is None or len(people_list) == 0:
        return []

    person_map = {person["id"]: person for person in people_list}

    results = []
    try:
        body_data = [{"id": person["id"]} for person in people_list]

        # 调用 Apollo API，获取邮箱
        response = apollo_client.post(
            "/people/bulk_match?reveal_personal_emails=true&reveal_phone_number=false",
            json={"details": body_data},
        )
        if response.status_code == 200:
            result = response.json()
            data_list = result.get("matches", [])

            for data in data_list:
                person_id = data["id"]
                if person_id in person_map:
                    # 保留部分必要字段
                    data["first_name"] = person_map[person_id].get("firstName", "")
                    data["last_name"] = person_map[person_id].get("lastName", "")
                    data["thinking"] = person_map[person_id].get("thinking", "")
                    data["priority"] = person_map[person_id].get("priority", "")
                    # 移除不必要的字段
                    data.pop("employment_history")
                    data.pop("organization")
                    results.append(data)
        else:
            logging.error(
                f"Apollo API 获取职员 Email 信息失败 - 状态码: {response.status_code}, 响应: {response.text}"  # noqa: E501
            )
    except Exception as e:
        logging.error(f"使用 Apollo API 批量获取职员 Email 信息时出错: {e}")

    return results


def enrich_people_phone(contact_id: str, linkedin_url: str = None, email: str = None) -> str | None:
    """使用 Apollo API 获取职员电话信息"""
    if linkedin_url is None and email is None:
        return None

    try:
        request_id = str(uuid.uuid4())
        logging.info(
            f"使用 Apollo API 获取职员电话信息 - 联系人ID: {contact_id}, LinkedIn URL: {linkedin_url}, Email: {email}, request_id: {request_id}"  # noqa: E501
        )

        webhook_url = f"{SERVER_DOMAIN}/api/sales-agent-webhook/apollo/{request_id}"
        query_params = {"reveal_phone_number": "true", "webhook_url": webhook_url}

        if linkedin_url:
            query_params["linkedin_url"] = linkedin_url
        elif email:
            query_params["email"] = email

        response = apollo_client.post("/people/match", params=query_params)
        if response.status_code == 200:
            # 请求成功后，返回 request_id
            return request_id
        else:
            logging.error(
                f"Apollo API 获取职员电话信息失败 - 状态码: {response.status_code}, 响应: {response.text}"  # noqa: E501
            )
    except Exception as e:
        logging.error(f"使用 Apollo API 获取职员电话信息时出错: {e}")

    return None


if __name__ == "__main__":
    company_name = "TeleChoice"  # 企业名称
    company_location = "Australia"  # 企业所在地区
    website = "http://www.telechoice.com.au/"
    # {'id': '54a11d8569702d7fe6152f01', 'name': 'TeleChoice'}
    organizations = search_organization(company_name, [company_location], website)
    logging.info(organizations)
