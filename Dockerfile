FROM python:3.12 as build

WORKDIR /app

ENV PYTHONPATH=/app
ENV PATH=/app/.venv/bin:$PATH

RUN sed -i 's|URIs: http://deb.debian.org/debian|URIs: http://mirrors.aliyun.com/debian|g' /etc/apt/sources.list.d/debian.sources

# 安装 uv
RUN pip install -i https://mirrors.aliyun.com/pypi/simple/ uv

COPY pyproject.toml ./
# 使用 uv sync 安装依赖
RUN uv sync --no-dev -i https://mirrors.aliyun.com/pypi/simple/

# only install playwright browsers if playwright command is available
RUN if command -v playwright >/dev/null 2>&1; then playwright install --with-deps; else echo "playwright command not found, skipping browser installation"; fi

# Add build argument for version
ARG GET_VERSION
ARG BUILD_TIME
ENV GET_VERSION=${GET_VERSION}
ENV BUILD_TIME=${BUILD_TIME}
ENV ENVIRONMENT=production
ENV LOG_LEVEL=INFO
ENV HTTPS_PROXY=
ENV HTTP_PROXY=
ENV NO_PROXY=*

# ====================================
FROM build as release

COPY . /app/

WORKDIR /app

# 暴露SSE服务端口
EXPOSE 5000

# 启动SSE服务
CMD ["python", "-m", "server.sse"]
