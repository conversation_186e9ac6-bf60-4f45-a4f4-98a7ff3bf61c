# 模块级别标识使用指南

## 概述

为了更好地追踪和调试系统错误，我们在日志系统中添加了模块级别标识功能。现在每条日志都会显示清晰的模块来源信息，方便快速定位问题所在的模块。

## 日志格式

新的日志格式如下：
```
Sales Agent 2025-08-12 17:15:54.378 INFO [模块名] 函数名:行号 - [task_id=xxx] 日志消息
```

### 格式说明

- `Sales Agent`: 系统前缀
- `时间戳`: 服务器时区的时间
- `日志级别`: INFO, WARNING, ERROR等
- `[模块名]`: **模块标识**
- `函数名:行号`: 具体的代码位置
- `[task_id=xxx]`: 任务上下文（如果存在）
- `日志消息`: 实际的日志内容

## 使用方法

### 最简洁的方案

在每个模块的顶部定义一个预配置的logger：

```python
from loguru import logger

# 在模块顶部定义一次
module_logger = logger.bind(module_name="server.sse_routes")

# 然后直接使用，无需重复bind
module_logger.info("开始处理请求")
module_logger.warning("检测到异常情况")
module_logger.error("处理失败", exc_info=True)
```

### 模块命名规范

- **服务器模块**: `server.模块名`
  - `server.sse_routes` - SSE路由
  - `server.data_processor` - 数据处理器
  - `server.task.task_manager` - 任务管理器
  - `server.task.base_task_processor` - 基础任务处理器

- **第三方API模块**: `api提供商.模块名`
  - `apollo.apollo_api` - Apollo API
  - `zoho.contacts_api` - Zoho联系人API
  - `zoho.accounts_api` - Zoho账户API

- **工具模块**: `utils.模块名`
  - `utils.logger` - 日志工具
  - `utils.http_client` - HTTP客户端

### 实际使用示例

**server/sse_routes.py:**
```python
from loguru import logger

# 模块专用日志记录器
logger = logger.bind(module_name="server.sse_routes")

@router.post("/start")
async def start_detect_contacts_task(data: dict):
    logger.info(f"开始处理请求: {data}")
    try:
        # 业务逻辑
        pass
    except Exception as e:
        logger.error(f"处理失败: {e}", exc_info=True)
```

**server/data_processor.py:**
```python
from loguru import logger

# 模块专用日志记录器
module_logger = logger.bind(module_name="server.data_processor")

class DataProcessor:
    def __init__(self, task_id: str):
        self.logger = module_logger
        
    def process_data(self, data):
        self.logger.info("开始处理数据")
        try:
            # 处理逻辑
            pass
        except Exception as e:
            self.logger.error(f"数据处理失败: {e}", exc_info=True)
```

## 示例输出

```
Sales Agent 2025-08-12 17:15:54.378 INFO [server.sse_routes] start_detect_contacts_task:28 - 开始处理请求: {'account_id': 'acc123'}
Sales Agent 2025-08-12 17:15:54.379 INFO [server.data_processor] process_data:95 - [task_id=task_abc123] 开始处理数据
Sales Agent 2025-08-12 17:15:54.380 ERROR [apollo.apollo_api] search_organization:78 - Apollo API调用失败: 连接超时
Sales Agent 2025-08-12 17:15:54.381 INFO [server.task.task_manager] post_execute:108 - [task_id=task_abc123] 任务执行完成
```

## 优势

1. **极简使用**: 每个模块只需一行代码定义logger
2. **无额外依赖**: 直接使用标准loguru API
3. **快速定位**: 通过模块名快速定位错误来源
4. **清晰分层**: 区分服务器模块、API模块、工具模块
5. **任务追踪**: 结合task_id追踪特定任务的执行流程

## 迁移指南

对于现有代码：

**之前:**
```python
from loguru import logger

logger.info("消息")
```

**现在:**
```python
from loguru import logger

# 在模块顶部添加一行
module_logger = logger.bind(module_name="模块名")

# 使用预配置的logger
module_logger.info("消息")
```

这样就能获得清晰的模块级别标识，代码改动最小，使用最简洁。
