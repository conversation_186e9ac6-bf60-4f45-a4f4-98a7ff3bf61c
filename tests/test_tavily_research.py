"""
Test file for gemini_research ReactAgent
"""

from research.account.tavily_research import TavilyResearch


async def test_tavily_research():
    """Test the TavilyResearch with Sullair research query"""
    tavily_research = TavilyResearch()

    # research query
    research_query = """请以中文（简体）撰写本次研究报告，并按照以下具体步骤与要求完成任务：

任务描述：
- 调研 https://www.augury.com/ 这家公司，重点确认其是否有使用边缘网关（edge gateway），如果有，明确他们使用的是哪家公司的网关或相关产品。

操作步骤与注意事项：
1. 公司基本信息
   - 简要说明Augury公司的业务领域和主营产品/解决方案。
   - 说明其客户群体和应用场景。

2. 边缘网关相关信息收集
   - 明确查找“边缘网关（edge gateway）”在其产品或解决方案中的应用情况。
   - 确认其产品或方案中是否有集成或采用边缘网关，如有：
     - 详细说明边缘网关的作用和应用场景。
     - 具体列出他们所使用的边缘网关产品或技术提供商（如：Cisco、HPE、Dell、Advantech等）。
   - 若未公开具体供应商，尽量通过新闻稿、案例分析、技术白皮书、合作伙伴页面等辅助信息进行间接确认，并说明未公开的情况。

3. 主要信息来源
   - 优先查阅Augury官网（https://www.augury.com/）的产品说明、新闻稿、技术文档、用户案例和解决方案页面。
   - 补充查找行业新闻（如Industrial IoT、Edge Computing等专业媒体）、官方合作伙伴页面以及技术论坛。
   - 如有必要，可关注其白皮书、演讲活动或官方博客内容。
   - 严禁采信非权威、SEO导向的博客或内容农场，所有资料尽量以官网、官方发布或权威媒体为主。
   - 请附上关键材料或页面的超链接（中文优先，如无中文资料请用英文来源）。

4. 输出格式要求
   - 以结构化报告形式输出，建议格式：
     - 一、公司简介
     - 二、边缘网关应用情况
     - 三、引用与信息来源
   - 信息要点分段清晰，描述准确简明。
   - 未明确或无法查证之处要特注明确无相关公开信息。
"""

    # run the agent
    result = await tavily_research.research(research_query)
    assert result is not None
    assert len(result) > 0

    # print results for inspection
    print("\n=== RESEARCH RESULT ===")
    from rich.console import Console
    from rich.markdown import Markdown

    console = Console()
    markdown_content = Markdown(result)
    console.print(markdown_content)
