import asyncio
import os
from unittest.mock import patch

import pytest
from langchain_aws import ChatBedrockConverse
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI

from utils.logging_callback_handler import logging_callback
from utils.messages import message_content
from utils.models import _get_default_thinking_budget, init_model


class TestGetDefaultThinkingBudget:
    """test cases for _get_default_thinking_budget function"""

    def test_get_default_thinking_budget_with_valid_integer(self):
        """test with valid integer environment variable"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "1024"}):
            result = _get_default_thinking_budget()
            assert result == 1024

    def test_get_default_thinking_budget_with_zero(self):
        """test with zero value environment variable"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "0"}):
            result = _get_default_thinking_budget()
            assert result == 0

    def test_get_default_thinking_budget_with_negative_value(self):
        """test with negative value environment variable"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "-100"}):
            result = _get_default_thinking_budget()
            assert result == -100

    def test_get_default_thinking_budget_with_large_value(self):
        """test with large integer value"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "999999"}):
            result = _get_default_thinking_budget()
            assert result == 999999

    def test_get_default_thinking_budget_without_env_var(self):
        """test when environment variable is not set"""
        with patch.dict(os.environ, {}, clear=True):
            result = _get_default_thinking_budget()
            assert result is None

    def test_get_default_thinking_budget_with_empty_string(self):
        """test with empty string environment variable"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": ""}):
            result = _get_default_thinking_budget()
            assert result is None

    def test_get_default_thinking_budget_with_invalid_string(self):
        """test with invalid string that cannot be converted to int"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "invalid"}):
            with pytest.raises(ValueError):
                _get_default_thinking_budget()

    def test_get_default_thinking_budget_with_float_string(self):
        """test with float string that cannot be converted to int directly"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "123.45"}):
            with pytest.raises(ValueError):
                _get_default_thinking_budget()

    def test_get_default_thinking_budget_with_whitespace(self):
        """test with whitespace in environment variable"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "  1024  "}):
            result = _get_default_thinking_budget()
            assert result == 1024

    def test_get_default_thinking_budget_with_scientific_notation(self):
        """test with scientific notation string"""
        with patch.dict(os.environ, {"LLM_THINKING_BUDGET": "1e3"}):
            with pytest.raises(ValueError):
                _get_default_thinking_budget()


async def test_model_stream_message():
    from langfuse.langchain import CallbackHandler

    model = init_model(
        "o4-mini-low",
        use_responses_api=True,
        # extra_body={"enable_thinking": True, "thinking_budget": 64},
    )

    async for chunk in model.astream("写一个笑话", config={"callbacks": [CallbackHandler()]}):
        print(message_content(chunk), end="", flush=True)


async def test_gemini_flash_instant():
    model = init_model(
        "gemini-2.5-flash-instant",
    )
    result = await asyncio.wait_for(
        model.ainvoke("写一个笑话"),
        timeout=10,
    )
    assert "<think>" not in message_content(result)


async def test_model_message():
    model = init_model(
        "o4-mini-low",
        use_responses_api=True,
    )
    assert isinstance(model, ChatOpenAI)
    result = await model.ainvoke("写一个笑话")
    print(message_content(result))


async def test_gemini_model():
    model = init_model(
        "gemini-2.5-flash-lite",
        thinking_budget=512,
        client_options={
            "api_endpoint": "apiproxy.inhand.ai",
        },
    )
    assert isinstance(model, ChatGoogleGenerativeAI)
    with logging_callback():
        result = await model.ainvoke("写一个笑话，并在最后输出你的模型")
        assert "<think>" in message_content(result)


async def test_openai_model():
    model = init_model("gpt-4.1-mini", base_url="https://apiproxy.inhand.ai/openai/v1")
    with logging_callback():
        result = await model.ainvoke("写一个笑话，并在最后输出你的模型")
        assert result is not None


async def test_openrouter_model():
    model = init_model("openrouter:moonshotai/kimi-k2")
    with logging_callback():
        await model.ainvoke("写一个笑话")


async def test_aliyun_kimi_k2():
    model = init_model("aliyun:Moonshot-Kimi-K2-Instruct")
    with logging_callback():
        await model.ainvoke("写一个笑话")


async def test_claude_model():
    from utils.agent import print_stream

    with patch.dict(os.environ, {"BEDROCK_BASE_URL": "", "BEDROCK_PROXY": "http://localhost:1087"}):
        model = init_model(
            "claude-3.5-haiku",
            thinking_budget=64,
        )

        assert isinstance(model, ChatBedrockConverse)
        await print_stream(model.astream("写一个冷笑话"))


async def test_claude_model_with_base_url():
    from utils.agent import print_stream

    with patch.dict(os.environ, {"BEDROCK_BASE_URL": "https://apiproxy.inhand.ai"}):
        model = init_model(
            "claude-3.5-haiku",
            thinking_budget=64,
        )

        assert isinstance(model, ChatBedrockConverse)
        await print_stream(model.astream("写一个冷笑话"))


async def test_disable_thinking():
    model = init_model(
        "gemini-2.5-flash-lite",
        thinking_budget=0,
    )
    result = await model.ainvoke("写一个冷笑话")
    print(message_content(result))

    # message content should not contains <think> tag
    assert "<think>" not in message_content(result)


async def test_openai_gpt_5_model():
    model = init_model("gpt-5")
    with logging_callback():
        result = await model.ainvoke("写一个笑话")
        assert result is not None


async def test_openai_gpt_5_mini():
    model = init_model("gpt-5-mini")
    with logging_callback():
        result = await model.ainvoke("写一个笑话")
        assert result is not None


async def test_openai_gpt_5_low():
    model = init_model("gpt-5-mini-low")
    with logging_callback():
        result = await model.ainvoke("写一个笑话")
        assert result is not None


async def test_openai_gpt_5_minimal():
    model = init_model("gpt-5-mini-minimal")
    with logging_callback():
        result = await model.ainvoke("写一个笑话")
        assert result is not None
