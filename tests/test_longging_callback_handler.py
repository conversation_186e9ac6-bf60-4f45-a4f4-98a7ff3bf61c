from dotenv import load_dotenv
from langchain_core.messages import HumanMessage
from langchain_core.tools import tool
from langgraph.prebuilt import create_react_agent

from utils.logging_callback_handler import LoggingCallbackHandler
from utils.models import init_model


@tool
def a_very_long_long_long_name_search_tool(input: str) -> str:
    """
    search the answer from the internet
    Args:
        input: the question to search for
    Returns:
        the answer to the question
    """
    return "Beijing"


async def test_logging_callback_handler():
    load_dotenv(".env.local", override=True)
    agent = create_react_agent(
        model=init_model("gpt-4.1-mini"),
        tools=[a_very_long_long_long_name_search_tool],
    )

    result = await agent.ainvoke(
        {"messages": [HumanMessage(content="Where is the capital of France? use search tool for the answer.")]},
        config={"callbacks": [LoggingCallbackHandler()], "tags": ["xxxx"]},
    )

    print(f"Final Answer: {result['messages'][-1].content}")
    # await print_stream_events()
