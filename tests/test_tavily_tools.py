import os
from unittest.mock import patch

from dotenv import load_dotenv

from agent.tools.tavily import tavily_search


class TestTavilySearch:
    """test cases for tavily_search function"""

    async def test_tavily_search_basic_query(self):
        """test basic search functionality with mock response"""
        with patch.dict(os.environ, {"MOCK_TAVILY_SEARCH": "1"}):
            result = await tavily_search.ainvoke(input={"query": "test query"})
            print(result)
            assert isinstance(result, dict)
            assert "query" in result
            assert "results" in result
            assert result["query"] == "test query"

    async def test_tavily_search(self):
        """test real API call with mocked TavilySearch"""
        load_dotenv(".env.local", override=True)
        result = await tavily_search.ainvoke(input={"query": """mastdigital.co.uk 公司的详细信息"""})
        assert isinstance(result, dict)
        assert "query" in result
        assert "results" in result

        print(f"Query: {result['query']}")
        print(f"Answer: {result['answer']}")
        print("Results:")
        for result in result["results"]:
            print(f"  Title: {result['title']}")
            print(f"  URL: {result['url']}")
            print(f"  Content: {result.get('content', 'N/A')}")
            print(f"  Score: {result.get('score', 'N/A')}")
            print(f"  Raw Content: {result.get('raw_content', 'N/A')}")
            print(f"  Images: {result.get('images', [])}")
            print("-" * 80)
