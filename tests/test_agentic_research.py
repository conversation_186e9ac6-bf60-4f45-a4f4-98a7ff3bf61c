from research.agentic_research import agentic_research


async def test_gitlab_releases_research():
    """Test to find all gitlab releases notes urls and sort them"""

    result = await agentic_research(
        research_topic="""
请帮我找到 GitLab 项目的所有发布版本说明页面链接，并按版本号排序。
不需要 patch 版本，只关注主版本和次版本。如：15.0, 15.1, 15.2
列出从17.0到最新版本的所有版本号和对应的发布说明 URL。
从最新到最老排列。
阅读每个版本说明，总结每个版本的主要变化。主要关注：免费本地部署版本就可以使用的功能。

你需要先收集所有版本号和对应的发布说明 URL，然后按版本号排序。确保每个版本号都存在，没有遗漏。
然后再阅读每个版本说明，总结每个版本的主要变化。

输出格式：
版本号: [版本号]
主要变化: [主要变化]
---
版本号: [版本号]
主要变化: [主要变化]
"""
    )
    assert result is not None
    assert len(result) > 0
    print("\n=== GITLAB RELEASES RESULT ===")
    print(result)
