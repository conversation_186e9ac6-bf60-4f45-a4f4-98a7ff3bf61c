from agent.tools.firecrawl_tools import firecrawl_scrape


async def test_firecrawl_scrape():
    result = await firecrawl_scrape.coroutine(
        "Extract company nature, services, verticals, regions, certifications, key technologies relevant to ITS system integration and industrial IoT networking.",  # noqa: E501
        ["https://smartek-its.com/index.php/about/"],
    )
    print(result)
    assert result is not None
