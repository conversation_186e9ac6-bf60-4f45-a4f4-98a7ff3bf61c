import pytest


@pytest.fixture(autouse=True)
def load_env():
    from utils import load_env

    load_env()


@pytest.fixture(autouse=True)
def apply_env_marker(monkeypatch, request):
    """
    自动处理 @pytest.mark.env("KEY=VALUE", ...) 标记，设置环境变量。
    """
    marker = request.node.get_closest_marker("env")
    if marker:
        for item in marker.args:
            if "=" in item:
                key, value = item.split("=", 1)
                monkeypatch.setenv(key, value)
