from agent.account.models import CompanyBasicInfo
from agent.account.research_company import research_company
from utils.logging_callback_handler import logging_callback


async def test_research_company():
    """Test research_company function with Sullair Australia data"""

    # Test data based on user input
    user_query = "find one"

    # Test with a company that has a website_url since research_company requires it
    # Using CJD Equipment which has a website_url
    company_with_website = CompanyBasicInfo(
        id="54a12acf69702d8cfcf27002",
        name="CJD Equipment",
        website_url="http://www.cjd.com.au",
        linkedin_url="https://www.linkedin.com/company/cjdequipment",
        organization_revenue="333M",
        phone="+61 3 6345 4100",
        founded_year=1974,
    )

    with logging_callback():
        # Test with company that has website_url
        result = await research_company(company_basic_info=company_with_website, user_query=user_query)

        print("=== RESEARCH COMPANY RESULT ===")
        print(f"Company name: {result.get('name')}")
        if "research_result" in result:
            print("Research completed successfully")
            print(f"Research result length: {len(result['research_result'])} characters")
        elif "error" in result:
            print(f"Research failed with error: {result['error']}")

        # Assert basic structure
        assert "name" in result
        assert result["name"] == company_with_website.name
        assert "research_result" in result or "error" in result


#  tavily_search CJD Equipment company profile
# scrape 'query': 'CJD Equipment company profile, main business, products, services, company history, partners, technology applications', 'urls': ['http://www.cjd.com.au']


async def test_research_company_missing_website():
    """Test research_company function with missing website_url to verify error handling"""

    user_query = "find one"

    # Test with company that has no website_url
    company_without_website = CompanyBasicInfo(
        id="55693f36736964219a581a00",
        name="KAESER Compressors Australia",
        website_url=None,
        linkedin_url="https://www.linkedin.com/company/kaeser-compressors-australia",
        organization_revenue=None,
        phone=None,
        founded_year=1990,
    )

    with logging_callback():
        result = await research_company(company_basic_info=company_without_website, user_query=user_query)

        print("=== RESEARCH COMPANY ERROR TEST RESULT ===")
        print(f"Company name: {result.get('name')}")
        print(f"Error: {result.get('error')}")

        # Should return error due to missing website_url
        assert "name" in result
        assert result["name"] == company_without_website.name
        assert "error" in result
        assert "Missing company website_url" in result["error"]
