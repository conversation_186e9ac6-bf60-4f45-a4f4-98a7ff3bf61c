import asyncio
from pathlib import Path

from langchain_core.prompts import ChatPromptTemplate
from langgraph.prebuilt import create_react_agent
from loguru import logger

from agent.account.models import SearchCompaniesResult
from agent.tools.apollo import get_organization_by_id, search_organizations
from utils import init_model
from utils.extractors import extract_structure_response
from utils.file_handler import load_file


async def search_target_companies(user_query: str) -> SearchCompaniesResult:
    """
    根据 user_query 搜索公司信息

    Args:
        user_query: 用户查询

    Returns:
        SearchCompaniesResult: 搜索公司信息结果
    """
    logger.info(f"开始搜索公司信息, user_query: {user_query}")

    try:
        llm = init_model(model="gpt-4.1")
        tools = [search_organizations, get_organization_by_id]

        system_prompt = load_file(Path(__file__).parent / "prompts" / "search_target_companies.md")
        prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("placeholder", "{messages}")])

        agent = create_react_agent(
            model=llm,
            tools=tools,
            prompt=prompt,
            name="search_target_companies_agent",
        )

        result = await agent.ainvoke({"messages": [("human", user_query)]})
        content = result.get("messages")[-1].text()

        if '"error"' in content:
            return SearchCompaniesResult(error="company not found", companies=[])

        return await extract_structure_response(content, SearchCompaniesResult)

    except Exception as e:
        logger.error(f"search company info failed: {e}")
        return SearchCompaniesResult(error=str(e), companies=[])


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()

    user_query = "Telsim，澳大利亚电信运营商"  # single result case
    # user_query = "Robertson"  # fuzzy name matching case - multiple results
    # user_query = "JDK，澳大利亚"  # multiple results case
    # user_query = "ripGinki，澳大利亚"  # not found case

    result = asyncio.run(search_target_companies(user_query))
    print(result.model_dump_json(exclude_none=True, indent=4))
