import asyncio
import json
from typing import List, NotRequired, Optional, TypedDict

import yaml
from langchain.callbacks.base import BaseCallbackHandler
from langchain_core.callbacks import dispatch_custom_event
from langchain_core.runnables import RunnableConfig
from langgraph.graph import END, StateGraph
from langgraph.graph.state import CompiledStateGraph
from loguru import logger

from agent.account.models import CompanyBasicInfo, LookalikeCompaniesResult, LookalikeCompanyInfo
from agent.account.research_company import analyze_ideal_customer_profile
from agent.account.search_lookalike_companies import search_lookalike_companies


class ResearchState(TypedDict):
    """调研状态数据结构"""

    # 输入信息
    user_query: str  # 用户查询需求
    company_info: dict  # 参考公司基本信息（用于 ICP 分析）
    excludes: NotRequired[List[str]]  # 排除的公司列表

    # 业务数据
    ideal_customer_profile: NotRequired[str]  # 理想客户画像分析结果
    lookalike_companies: NotRequired[List[LookalikeCompanyInfo]]  # lookalike公司列表
    title: NotRequired[str]  # 任务标题


class ResearchStateGraph:
    """调研工作流状态图"""

    def __init__(self, callbacks: Optional[list[BaseCallbackHandler]] = None, config: Optional[RunnableConfig] = None):
        self.callbacks = callbacks
        self.config = config
        self.graph = self._create_graph()

    def _create_graph(self) -> CompiledStateGraph:
        """创建状态图"""
        workflow = StateGraph(ResearchState)

        # 添加业务节点
        workflow.add_node("analyze_ideal_customer_profile", self._analyze_ideal_customer_profile_node)
        workflow.add_node("search_lookalike_companies", self._search_lookalike_companies_node)

        # 设置入口点
        workflow.set_entry_point("analyze_ideal_customer_profile")

        # 设置边
        workflow.add_edge("analyze_ideal_customer_profile", "search_lookalike_companies")
        workflow.add_edge("search_lookalike_companies", END)

        graph = workflow.compile()

        # 处理配置 - 合并 callbacks 和自定义 config
        final_config = None
        if self.callbacks and len(self.callbacks) > 0:
            final_config = RunnableConfig(callbacks=self.callbacks)

        if self.config:
            if final_config:
                # 合并配置
                final_config = RunnableConfig(
                    callbacks=final_config.get("callbacks", []),
                    metadata={**(final_config.get("metadata", {})), **(self.config.get("metadata", {}))},
                    recursion_limit=self.config.get("recursion_limit", 50),
                )
            else:
                final_config = self.config

        if final_config:
            graph = graph.with_config(config=final_config)

        return graph

    async def _analyze_ideal_customer_profile_node(self, state: ResearchState) -> ResearchState:
        """获取理想客户画像分析节点"""
        company_info = state["company_info"]
        name = company_info.get("name", "the reference company")
        website = company_info.get("website", "")

        dispatch_custom_event(
            "thinking",
            f"I will first analyze the reference company {name} to understand the Ideal Customer Profile (ICP) based on its business model, products, and characteristics.",  # noqa: E501
        )

        # 分析理想客户画像
        ideal_customer_profile = await analyze_ideal_customer_profile(
            company_basic_info=CompanyBasicInfo(name=name, website_url=website),
            user_query=state["user_query"],
        )

        return {"ideal_customer_profile": ideal_customer_profile}

    async def _search_lookalike_companies_node(self, state: ResearchState) -> ResearchState:
        """根据理想客户画像搜索lookalike公司节点"""
        company_name = state["company_info"].get("name", "the reference company")
        dispatch_custom_event(
            "thinking",
            f"Based on the ICP analysis of {company_name}, I will search for companies that match the ideal customer profile. Next, I will gradually achieve this goal.",  # noqa: E501
        )

        ideal_customer_profile = state.get("ideal_customer_profile")

        lookalike_companies: LookalikeCompaniesResult = await search_lookalike_companies(
            user_query=state["user_query"],
            reference_company=f"{yaml.dump(state['company_info'])}\n\n{ideal_customer_profile}",
            excludes=state.get("excludes"),
        )

        state["title"] = lookalike_companies.title
        state["lookalike_companies"] = lookalike_companies.companies

        company_names = ", ".join([company.name for company in lookalike_companies.companies])
        dispatch_custom_event(
            "thinking", f"I have searched for {len(state['lookalike_companies'])} lookalike companies: {company_names}."
        )
        return state

    async def execute(self, initial_state: ResearchState) -> dict:
        """执行调研工作流"""
        logger.debug("开始执行调研工作流...")
        try:
            final_state: ResearchState = await self.graph.ainvoke(initial_state)
            logger.debug("调研工作流执行完成")

            # 校验返回结果
            lookalike_companies = final_state["lookalike_companies"]
            result_data = [company.model_dump(exclude_none=True) for company in lookalike_companies]

            return {
                "title": final_state["title"],
                "data": result_data,
            }
        except Exception as e:
            logger.error(f"调研工作流执行失败: {str(e)}")
            raise Exception(f"execute research workflow failed: {str(e)}")


async def execute_research_workflow(
    user_query: str,
    company_info: dict,
    excludes: list[str],
    callbacks: Optional[list[BaseCallbackHandler]] = None,
) -> dict:
    """
    执行调研工作流

    Args:
        user_query: user query, customer prompt
        company_info: CompanyInfo
        excludes: list of company names to exclude
        callbacks: list of callback handlers, used to handle the event stream

    Returns:
        dict: 调研结果
        {
            "title": "...", # 标题
            "data": [] # 调研结果
        }
    """
    name = company_info.get("name", "")
    website = company_info.get("website", "")
    if not name and not website:
        raise Exception("Missing company name or website")

    # 排除当前公司
    if len(excludes) == 0 or name not in excludes:
        excludes.append(name)

    initial_state = ResearchState(
        user_query=user_query,
        company_info=company_info,
        excludes=excludes,
    )
    research_state_graph = ResearchStateGraph(callbacks=callbacks)
    return await research_state_graph.execute(initial_state)


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()
    # 运行示例

    async def main():
        user_query = "I want to know companies like iiNet rand who use edge gateways for its networking"  # noqa: E501
        company_info = {
            "name": "iiNet",
            "website": "http://www.iinet.net.au/",
            "description": "iiNet is an industry-leading Australian internet provider with services available nationwide. With our wide range of NBN plans and our own ULTRA Broadband Cable, FTTB & VDSL2 networks, we make it easy for Aussies to connect to great-value broadband.",  # noqa: E501
            "territory": "Australia",
            "industry": "ICT",
            "market_segments": ["DA"],
        }

        excludes = ["Cirrus Link Solutions", "Ingersoll Rand", "Inhand Networks"]

        result = await execute_research_workflow(user_query, company_info, excludes)
        logger.info(json.dumps(result, ensure_ascii=False, indent=4))

    asyncio.run(main())
