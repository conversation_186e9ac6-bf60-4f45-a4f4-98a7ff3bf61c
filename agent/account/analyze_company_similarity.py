from typing import Annotated, List, Optional

import yaml
from langchain_core.output_parsers import PydanticOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import (
    RunnableConfig,
    RunnableLambda,
    RunnableSerializable,
)
from langchain_core.tools import tool
from langgraph.prebuilt import InjectedState
from loguru import logger
from pydantic import BaseModel, Field

from agent.account.models import CompanySimilarityAnalysis
from agent.tools.apollo import apollo
from utils import init_model

# Similarity threshold for filtering companies
SIMILARITY_THRESHOLD = 0.6

similarity_chain: Optional[RunnableSerializable] = None


def get_similarity_chain() -> RunnableSerializable:
    """build and cache a similarity analysis chain"""
    global similarity_chain

    if similarity_chain is None:
        # use llm to analyze similarity
        llm = init_model(
            # model="o4-mini-low",
            max_tokens=4096,
            model="gpt-5-mini-low",
            text={"verbosity": "low"},
            # include_thoughts=True,
            # thinking_budget=256,
        ).bind_tools([{"type": "web_search_preview"}])

        output_parser = PydanticOutputParser(pydantic_object=CompanySimilarityAnalysis)

        # build analysis prompt
        prompt = ChatPromptTemplate.from_template(
            """
You are a business analyst expert. Analyze the similarity between target company and reference company
based on user requirements.

## Analysis Task:
1. Calculate similarity score (0-1, where 1 is perfect match) based on user requirements
2. Provide a concise one-sentence description of the similarity
3. List specific similarity reasons and key differences
4. Follow the lookalike criteria in `Target Company Information` section to determine the similarity score
5. Use search tool to get more information about the target company if needed

## Similarity Criteria:

1. Meet all essential conditions
2. If any exclusion condition is met, it is considered not similar
3. Meet all verification criteria

## Output language

1. Default working language: **English**
2. Use the language specified by user in messages as the working language when explicitly provided
3. All thinking and responses must be in the working language
4. Natural language arguments in tool calls must be in the working language
5. Avoid using pure lists and bullet points format in any language

## Output fields
1. description:
    if similarity_score >= {similarity_threshold}, list all specific similarity reasons
    if similarity_score < {similarity_threshold}, list all specific differences or exclusion reasons
2. company_name: the name of the target company
3. similarity_score: the similarity score between the target company and the reference company

{format_instructions}

Focus on aspects most relevant to the user requirements when calculating similarity.
Use precise decimal values for similarity_score (e.g., 0.85, 0.62, 0.91).

## User Requirements:
```
{user_query}
```

## Reference Company Information:
```
{reference_company}
```

## Target Company Information:

```yaml
{target_company_info}
```
"""
        ).partial(
            similarity_threshold=SIMILARITY_THRESHOLD,
            format_instructions=output_parser.get_format_instructions(),
        )
        similarity_chain = prompt | llm | output_parser

    return similarity_chain


async def analyze_company_similarity(
    id: str,
    name: str,
    location: str | None = None,
    website_url: str | None = None,
    user_query: str = "",
    reference_company: str = "",
) -> str:
    """
    internal method to analyze similarity for a single company

    Args:
        id: The unique Apollo ID of the organization
        name: The name of the organization to search for
        location: The location of the organization (city, state, or country)
        website_url: The website URL of the organization
        user_query: User query containing requirements
        reference_company: Reference company information

    Returns:
        Single company similarity analysis result in text format
    """
    logger.info(
        f"Starting company similarity analysis - ID: {id}, Name: {name}, Location: {location}, Website: {website_url}"
    )

    if not user_query or not reference_company:
        error_msg = "Missing user_query or reference_company"
        logger.error(error_msg)
        return f"Error: {error_msg}"

    # fetch complete organization info
    target_company_info = await apollo.get_complete_organization_info(
        id=id,
        name=name,
        location=location,
        website_url=website_url,
    )
    yaml_company_info = yaml.dump(target_company_info)

    similarity_analysis: CompanySimilarityAnalysis = await get_similarity_chain().ainvoke(
        {
            "user_query": user_query,
            "reference_company": reference_company,
            "target_company_info": yaml_company_info,
        },
        config=RunnableConfig(run_name="analyze_company_similarity"),
    )

    if similarity_analysis.error:
        logger.error(f"Analysis failed: {similarity_analysis.error}")
        return f"Error: {similarity_analysis.error}"

    # decide response content based on score (filtering by code, not llm)
    should_filter = similarity_analysis.similarity_score < SIMILARITY_THRESHOLD
    company_name = similarity_analysis.company_name

    if should_filter:
        logger.info(
            (
                f"Company {company_name} similarity "
                f"{similarity_analysis.similarity_score:.2f} < {SIMILARITY_THRESHOLD}, filtered"
            )
        )
        filter_response = (
            f"Company: {company_name}\n"
            f"Similarity Score: {similarity_analysis.similarity_score:.2f}\n"
            f"Status: Filtered (score < {SIMILARITY_THRESHOLD})\n"
            f"Reason: {similarity_analysis.explanation or 'No specific reason provided'}"
        )

        return filter_response
    else:
        logger.info(
            f"Company {company_name} similarity {similarity_analysis.similarity_score:.2f} >= {SIMILARITY_THRESHOLD}"
        )
        pass_response = (
            f"Company: {company_name}\n"
            f"Similarity Score: {similarity_analysis.similarity_score:.2f}\n"
            f"Status: Recommended (score >= {SIMILARITY_THRESHOLD})\n"
            f"Reason: {similarity_analysis.explanation or 'No specific reason provided'}\n"
            f"\nDetailed Company Info:\n{yaml_company_info}"
        )
        return pass_response


class CompanyForAnalysis(BaseModel):
    """company info for similarity analysis"""

    id: str = Field(description="Apollo organization ID")
    name: str = Field(description="Company name")
    location: Optional[str] = Field(default=None, description="Company location")
    website_url: Optional[str] = Field(default=None, description="Company website URL")


@tool(parse_docstring=True)
async def bulk_analyze_company_similarity(
    companies: List[CompanyForAnalysis],
    user_query: Annotated[str, InjectedState("user_query")] = "",
    reference_company: Annotated[str, InjectedState("reference_company")] = "",
) -> str:
    """
    Batch analyze company similarity by comparing multiple target companies with reference company
    based on user requirements.

    This tool processes multiple companies in parallel for efficiency.

    Args:
        companies: List of CompanyForAnalysis objects containing company information
        user_query: Injected state containing user_query
        reference_company: Injected state containing reference_company

    Returns:
        Batch company similarity analysis results in text format
    """
    logger.info(f"Starting batch analysis for {len(companies)} companies")

    if not user_query or not reference_company:
        error_msg = "Missing user_query or reference_company in state"
        logger.error(error_msg)
        return f"Error: {error_msg}"

    if not companies:
        return "Error: No companies provided for analysis"

    async def analyze_one(company: CompanyForAnalysis) -> str:
        try:
            return await analyze_company_similarity(
                id=company.id,
                name=company.name,
                location=company.location,
                website_url=company.website_url,
                user_query=user_query,
                reference_company=reference_company,
            )
        except Exception as e:
            return f"Company: {company.name}\nError: {str(e)}"

    results = await RunnableLambda(analyze_one).abatch(companies, config=RunnableConfig(max_concurrency=10))

    return "\n---\n".join(results)
