from langchain_core.tools import tool


@tool(parse_docstring=True)
def think(thought: str) -> str:
    """
    Use the tool to think about something. It will not obtain new information or change the database
    , but just append the thought to the log. Use it when complex reasoning or some cache memory
    is needed.

    Args:
        thought: A detailed step-by-step thought to think about and reason through, be specific.

    Returns:
        A confirmation that the thought has been processed
    """
    # dispatch_custom_event("thinking", thought)
    return "ok"
