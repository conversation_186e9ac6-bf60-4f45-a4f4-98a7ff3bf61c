import asyncio
import functools
import hashlib
import os
from pathlib import Path

from diskcache import <PERSON><PERSON>
from firecrawl.firecrawl import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, FirecrawlDocument, ScrapeOptions
from langchain_core.tools import ToolException, tool
from loguru import logger

from utils import init_model

app: FirecrawlApp = None
concurrent_browsers = asyncio.Semaphore(value=2)

# cache directory for scrape results
CACHE_DIR = Path(__file__).parent / ".cache/firecrawl"
CACHE_DIR.mkdir(parents=True, exist_ok=True)

cache = Cache(
    directory=str(CACHE_DIR),
    eviction_policy="least-recently-used",
    size_limit=int(1e9),
    cull_limit=10,
)


def _get_cache_key(url: str) -> str:
    """generate cache key from URL"""
    return hashlib.md5(url.encode()).hexdigest()


def _get_firecrawl_app():
    global app
    if app is None:
        app = FirecrawlApp()
    return app


async def scrape_url(url: str, use_cache: bool = True) -> FirecrawlDocument:
    """
    Scrape a URL using Firecrawl and return the markdown content

    Args:
        url: the URL to scrape
        use_cache: whether to use cached results if available

    Returns:
        FirecrawlDocument with scraped content
    """
    # try to load from cache first
    if use_cache:
        data = cache.get(url)
        if data is not None:
            logger.info(f"Loaded from cache: {url}")
            return FirecrawlDocument(**data)

    # scrape the URL if not in cache or cache disabled
    try:
        doc = _get_firecrawl_app().scrape_url(url, formats=["markdown"])

        # save to cache if enabled
        cache.set(url, doc.model_dump(), expire=60 * 60 * 24 * 7)

        return doc

    except Exception as e:
        logger.error(f"❌ error scraping URL {url}: {e}")
        raise


@tool
async def firecrawl_scrape(query: str, urls: list[str]) -> str:
    """
    Scrape URLs using Firecrawl and analyze content based on query

    Args:
        query: search query string for the scraped contents
        urls: list of URLs to scrape

    Returns:
        analyzed and summarized content based on the query
    """

    def prepare_document(doc: FirecrawlDocument) -> str:
        result = "URL: " + (doc.url or doc.metadata.get("url", "Unknown URL")) + "\n"
        if doc.markdown:
            result += "Content:\n" + doc.markdown + "\n"
        if doc.links:
            result += "Links:\n" + "\n".join(doc.links) + "\n"
        return result

    try:
        # scrape the URL content with semaphore
        async with concurrent_browsers:
            if os.environ.get("APP_ENV") == "development":
                documents = await asyncio.gather(*[scrape_url(url) for url in urls])
            else:
                result = _get_firecrawl_app().batch_scrape_urls(
                    urls=urls, formats=["markdown"], only_main_content=False
                )
                documents = result.data
            logger.info(f"Successfully scraped URLs: {urls}")

        # extract the content from the result
        scraped_content = "\n".join([prepare_document(doc) for doc in documents])

        # use LLM to analyze and summarize based on query
        analysis_llm = init_model(model="gemini-2.5-flash-instant", max_tokens=4096, temperature=0)

        summary_prompt = f"""
请根据以下查询需求，分析并总结提供的网页内容：

## 要求
- 请提供相关的分析和总结，重点关注与查询需求相关的信息
- 确保回答完整、明确、明了，突出关键信息
- 直接回复结果，不要添加任何解释和总结
- 只针对查询需求中提到的内容进行回答，删除多余的完全无用网页内容
- 分析查询需求，保留与查询需求可能相关的网页内容
- 只从网页内容中抽取与查询需求相关的内容，不要添加你自己的分析和总结

<查询需求>
{query}
</查询需求>

<网页内容>
{scraped_content}
</网页内容>


## 输出要求
- Keep the links in the content for further research.
- The language of the output should be the same as the language of the web page.
"""

        # get analysis from LLM
        summary = await analysis_llm.ainvoke(summary_prompt)
        analyzed_content = summary.text()

        # collect all links from documents
        all_links = []
        for doc in documents:
            if doc.links:
                all_links.extend(doc.links)
            if doc.metadata and "url" in doc.metadata:
                all_links.append(doc.metadata["url"])
        if all_links:
            return analyzed_content + "\n\nLinks:\n" + "\n".join(all_links)
        return analyzed_content

    except Exception as e:
        import traceback

        logger.error(f"Error scraping and analyzing URLs {urls}: {e}\n{traceback.format_exc()}")
        return f"Error scraping and analyzing URLs: {str(e)}"


@tool(parse_docstring=True)
def firecrawl_crawl(url: str, limit: int = 10, formats: list[str] | None = None) -> str:
    """
    Crawl the url and all accessible sub pages and return the markdown for each one.

    Args:
        url: the starting URL to crawl
        limit: maximum number of pages to crawl (default: 10)
        formats: list of output formats, default is ['markdown', 'html']

    Returns:
        crawl results
    """
    if formats is None:
        formats = ["markdown", "html"]

    try:
        scrape_options = ScrapeOptions(formats=formats)
        result = _get_firecrawl_app().crawl_url(url, limit=limit, scrape_options=scrape_options)
        logger.info(f"Successfully crawled URL: {url} with limit: {limit}")
        return str(result)
    except Exception as e:
        logger.error(f"Error crawling URL {url}: {e}")
        return f"Error crawling URL: {str(e)}"


@tool(parse_docstring=True)
@functools.lru_cache(maxsize=50)
def firecrawl_map(
    url: str,
    search: str | None = None,
    ignore_sitemap: bool | None = None,
    include_subdomains: bool | None = None,
    sitemap_only: bool | None = None,
    limit: int | None = None,
) -> str:
    """
    Map a website to get list of URLs using Firecrawl

    Args:
        url: the website URL to map
        search: filter pattern for URLs
        ignore_sitemap: skip sitemap.xml processing
        include_subdomains: include subdomain links
        sitemap_only: only use sitemap.xml
        limit: maximum URLs to return

    Returns:
        list of URLs found on the website
    """
    try:
        result = _get_firecrawl_app().map_url(
            url,
            search=search,
            ignore_sitemap=ignore_sitemap,
            include_subdomains=include_subdomains,
            sitemap_only=sitemap_only,
            limit=limit,
        )
        if result.success:
            return "\n".join(result.links)
        raise ToolException(f"Error mapping URL: {result.error}")
    except Exception as e:
        logger.error(f"Error mapping URL {url}: {e}", exc_info=True)
        raise ToolException(f"Error mapping URL: {str(e)}")


if __name__ == "__main__":
    # test the semaphore in an async context
    from dotenv import load_dotenv

    load_dotenv()
    load_dotenv(".env.local")

    async def test_semaphore():
        async with concurrent_browsers:
            await firecrawl_scrape.ainvoke(
                {
                    "query": "What is the main content of the page?",
                    "urls": ["https://www.google.com"],
                }
            )

    asyncio.run(test_semaphore())
