import asyncio
import json
from typing import Any, Dict, Optional

from langchain_core.messages import ToolMessage
from langchain_core.prompts import (
    ChatPromptTemplate,
    HumanMessagePromptTemplate,
    SystemMessagePromptTemplate,
)
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from loguru import logger

from agent.tools import think
from apollo.apollo_api import search_organization
from config import MODEL
from utils.file_handler import load_file


@tool(parse_docstring=True)
def search_target_company(
    q_organization_name: Optional[str] = None,
    organization_locations: Optional[list[str]] = None,
    organization_website: Optional[str] = None,
) -> str:
    """
    使用 Apollo API 搜索企业信息。有几个过滤器可以帮助缩小搜索范围，如果搜索出多个结果，请结合输入信息，选择最合适的企业返回。
    所有参数必须使用英文。

    Args:
        q_organization_name: Filter search results to include a specific company name.
        organization_locations: The location of the company headquarters. You can search across cities, US states, and countries.
        organization_website: The website of the company. This is a good way to filter out results that are not relevant to your search.

    Returns:
        包含搜索结果的JSON字符串
    """  # noqa: E501
    logger.info(
        f"正在搜索企业信息，"
        f"name: {q_organization_name}, locations: {organization_locations}, website: {organization_website}"
    )
    result = search_organization(
        q_organization_name=q_organization_name,
        organization_locations=organization_locations,
        organization_website=organization_website,
    )
    if "error" in result:
        return result["error"]
    return json.dumps(result)


async def search_company(company_name: str, company_location: str, organization_website: str) -> Dict[str, Any] | None:
    """
    搜索并选择最匹配的目标企业

    Args:
        company_name: 企业名称
        company_location: 企业所在地区
        organization_website: 企业官网

    Returns:
        包含目标企业信息的字典，格式为：
        {
            "id": "企业ID",
            "name": "企业名称",
            "match_reason": "选择理由"
        }
        若未搜到目标企业或发生错误，返回 None
    """
    try:
        # 初始化agent
        agent = create_react_agent(
            model=ChatOpenAI(model_name=MODEL),
            tools=[search_target_company, think],
            name="search_company",
        )
        # 准备输入
        system_prompt = load_file("prompts/search_company_prompt.md")
        user_prompt = (
            f"请帮我搜索并选择最匹配的企业。"
            f"企业名称：{company_name}，"
            f"所在地区：{company_location}，"
            f"企业网站：{organization_website}"
        )
        # 构建提示词模板
        prompt_template = ChatPromptTemplate.from_messages(
            [
                SystemMessagePromptTemplate.from_template(system_prompt),
                HumanMessagePromptTemplate.from_template(user_prompt),
            ]
        )
        # 执行agent
        result = await agent.ainvoke({"messages": prompt_template.format_messages()})

        # 验证结果
        if not result or not result.get("messages"):
            logger.error("未收到有效结果")

        last_message = result["messages"][-1]
        if isinstance(last_message, ToolMessage) and last_message.status == "error":
            logger.error(f"工具执行错误: {last_message.content}")

        if last_message.content == "[]" or last_message.content == "{}":
            logger.warning(f"搜索结果为空: {last_message.content}")
            return None

        # 解析结果
        try:
            json_data = json.loads(last_message.content)
            if json_data and json_data.get("id") and json_data.get("id") != "":
                logger.info(f"搜索企业结果: {json_data}")
                return json_data
            else:
                logger.error(f"搜索结果为空: {last_message.content}")
                return None
        except json.JSONDecodeError:
            logger.error(f"无法解析结果: {last_message.content}")

    except Exception as e:
        logger.error(f"搜索过程发生错误: {str(e)}")

    return None


if __name__ == "__main__":

    async def main():
        company_name = "Qu POS"
        company_location = "United States"
        company_website = "http://www.qubeyond.com/"
        await search_company(company_name, company_location, company_website)

    asyncio.run(main())
