from typing import Optional

from langchain_core.messages import ToolMessage
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import Chat<PERSON>rompt<PERSON>emplate
from langgraph.prebuilt import create_react_agent
from loguru import logger

from agent.contacts_agent_state import ContactsAgentState
from agent.filter_people_tools import filter_people, think_people_search_strategy
from agent.search_apollo_tools import search_apollo_people, search_organization
from agent.search_linkedin_tools import get_linkedin_profiles
from agent.tools import tavily_search, think
from config import CONTACTS_AGENT_MODEL, CONTACTS_RECURSION_LIMIT
from utils.file_handler import load_file
from utils.models import init_model

MAX_CONSECUTIVE_TOOL_ERRORS = 3


class ContactsAgent:
    """
    联系人搜索代理
    """

    def __init__(
        self,
        exclude_linkedin_urls: Optional[list[str]] = None,
    ):
        if exclude_linkedin_urls is None:
            exclude_linkedin_urls = []

        self._initial_state = {
            "exclude_linkedin_urls": exclude_linkedin_urls,
            "search_strategy": "",
            "found_people": {},
        }

        # CONTACTS_AGENT_MODEL = "gpt-4.1"
        self.llm = init_model(model=CONTACTS_AGENT_MODEL, temperature=0.1, max_tokens=2048)
        logger.info(f"Initializing ContactsAgent with model: {CONTACTS_AGENT_MODEL}")
        self._init_agent()

    def _post_model_hook(self, state: ContactsAgentState) -> dict:
        """Hook to intervene if tools fail consecutively."""
        try:
            current_decision = state["messages"][-1]

            if not isinstance(current_decision, ToolMessage):
                return {}

            consecutive_errors = self._count_consecutive_errors(state)

            if consecutive_errors >= MAX_CONSECUTIVE_TOOL_ERRORS:
                error_msg = f"Tools failed {consecutive_errors} consecutive times. Aborting agent execution."
                logger.error(error_msg)
                raise RuntimeError(error_msg)

            return {}

        except RuntimeError:
            raise
        except Exception as e:
            logger.error(f"Error in _post_model_hook: {e}")
            return {}

    def _count_consecutive_errors(self, state: ContactsAgentState) -> int:
        """Count consecutive tool errors in recent messages."""
        all_messages = state["messages"]

        # 获取最近的 n 条消息
        recent_messages = all_messages[-MAX_CONSECUTIVE_TOOL_ERRORS:]

        # 生成布尔数组：这个消息有没有错误
        error_array: list[bool] = [isinstance(msg, ToolMessage) and self._is_tool_error(msg) for msg in recent_messages]

        if not any(error_array):
            return 0

        # 统计 error_array 中 True 的个数
        return sum(error_array)

    def _is_tool_error(self, tool_msg) -> bool:
        """Check if a tool message represents an error."""
        if not isinstance(tool_msg, ToolMessage):
            return False
        return getattr(tool_msg, "status", None) == "error"

    def _init_agent(self):
        tools = [
            search_organization,
            tavily_search,
            think_people_search_strategy,
            search_apollo_people,
            filter_people,
            get_linkedin_profiles,
            think,
        ]
        prompt = ChatPromptTemplate.from_messages(
            [("system", self._get_system_prompt()), ("placeholder", "{messages}")]
        )
        # model = self.llm.bind_tools(tools, parallel_tool_calls=False)
        model = self.llm.bind_tools(tools)
        self.agent = create_react_agent(
            model=model,
            tools=tools,
            prompt=prompt,
            state_schema=ContactsAgentState,
            name="contacts_agent",
            post_model_hook=self._post_model_hook,
        )

    def _get_system_prompt(self):
        return load_file("prompts/detect_contacts_prompt.md")

    def _prepare_input(self, user_query: str, organization_id: str) -> dict:
        return {
            "messages": [
                f"用户要求：{user_query}",
                f"组织 ID: {organization_id}",
                "请找出合适的潜在联系人",
            ],
            **self._initial_state,
        }

    async def ainvoke(self, user_query: str, organization_id: str) -> str:
        input_data = self._prepare_input(user_query, organization_id)
        result = await self.agent.ainvoke(
            input=input_data,
            config={"recursion_limit": CONTACTS_RECURSION_LIMIT},
        )
        return result["messages"][-1].text()

    async def get_contacts(self, user_query: str, organization_id: str) -> list[dict]:
        result = await self.ainvoke(user_query=user_query, organization_id=organization_id)
        try:
            return JsonOutputParser().parse(result)
        except Exception as e:
            logger.opt(exception=e).error(f"parse contacts result error: {result}")
            return []
