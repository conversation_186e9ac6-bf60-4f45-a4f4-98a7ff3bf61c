import asyncio
from pathlib import Path

import httpx
import yaml
from diskcache import Cache
from langchain_core.runnables import chain
from langchain_core.tools import tool

from config import (
    ENVIRONMENT,
    MAX_KEEPALIVE_CONNECTIONS,
    RAPID_API_HOST,
    RAPID_API_KEY,
    RAPID_API_URL,
)
from utils.logger import get_logger

logging = get_logger(__name__)

# 初始化缓存相关变量
linkedin_cache = None
CACHE_EXPIRY = 30 * 24 * 60 * 60  # 30天

if ENVIRONMENT == "local":
    # 创建缓存目录和Cache对象
    CACHE_DIR = Path("agent/cache")
    CACHE_DIR.mkdir(parents=True, exist_ok=True)

    # 初始化diskcache，设置缓存目录
    linkedin_cache = Cache(
        directory=str(CACHE_DIR / "linkedin_profiles"),
        eviction_policy="least-recently-used",
        size_limit=int(1e9),
        cull_limit=10,
    )

# 创建RapidAPI客户端
rapidapi_client = httpx.AsyncClient(
    timeout=30.0,
    headers={
        "x-rapidapi-host": RAPID_API_HOST,
        "x-rapidapi-key": RAPID_API_KEY,
    },
    limits=httpx.Limits(max_keepalive_connections=MAX_KEEPALIVE_CONNECTIONS),
)

# 限制并发请求数
MAX_CONCURRENCY = 2


def extract_linkedin_username(profile_url: str) -> str:
    """
    从LinkedIn个人资料URL中提取用户名

    Args:
        profile_url: LinkedIn个人资料URL，如 https://www.linkedin.com/in/username

    Returns:
        提取出的用户名
    """
    # 处理空URL情况
    if not profile_url:
        return ""

    # 如果URL包含问号，去掉问号及其后面的内容
    if "?" in profile_url:
        profile_url = profile_url.split("?")[0]

    # 如果URL以斜杠结尾，去掉结尾的斜杠
    if profile_url.endswith("/"):
        profile_url = profile_url[:-1]

    # 提取/in/后面的部分作为用户名
    if "/in/" in profile_url:
        username = profile_url.split("/in/")[-1]
        return username

    # 如果URL格式不符合预期，返回原始URL
    logging.warning(f"Invalid LinkedIn URL: {profile_url}")
    return profile_url


async def get_linkedin_profile(profile_url: str) -> dict:
    """
    内部函数：获取单个LinkedIn用户资料

    Args:
        profile_url: LinkedIn个人资料URL

    Returns:
        包含用户资料数据的字典或错误信息
    """
    # 设置参数
    params = {"username": extract_linkedin_username(profile_url)}

    # 发送请求
    response = await rapidapi_client.get(RAPID_API_URL, params=params)

    # 检查请求是否成功
    if response.status_code == 200:
        result = response.json()
        if result.get("success", True):
            data = result.get("data", {})
            data["connections"] = result.get("connection", 0)
            data["followers"] = result.get("follower", 0)
            return data
        else:
            message = result.get("message", "")
            logging.warning(f"failed to fetch LinkedIn profile: {profile_url}, {message}")
            return {"linkedin_url": profile_url, "error": message}
    else:
        response.raise_for_status()


def simplify_linkedin_profile(profile_data: dict) -> dict:
    """
    Simplify the LinkedIn profile data to the fields needed for candidate analysis.
    """
    try:
        simplified_data = {
            "name": f"{profile_data.get('firstName', '')} {profile_data.get('lastName', '')}",
            "headline": profile_data.get("headline", ""),
            "location": profile_data.get("geo", {}).get("full", ""),
            "connections": profile_data.get("connections", 0),
            "followers": profile_data.get("followers", 0),
            "summary": profile_data.get("summary", ""),
            "current_position": None,
            "skills": "",
        }

        # extract current position
        if "position" in profile_data and profile_data["position"]:
            current_position = profile_data["position"][0]
            simplified_data["current_position"] = {
                "title": current_position.get("title", ""),
                "company": current_position.get("multiLocaleCompanyName", {}).get("en_US", ""),
                "description": current_position.get("description", ""),
            }

        # extract top skills
        if "skills" in profile_data and profile_data["skills"]:
            simplified_data["skills"] = ", ".join([skill.get("name", "") for skill in profile_data["skills"]])
        return simplified_data
    except Exception as e:
        logging.warning(f"Error simplifying profile data: {str(e)}")
        return profile_data


@chain
async def memoed_get_linkedin_profile(profile_url: str) -> dict:
    logging.info(f"获取LinkedIn资料: {profile_url}")
    profile_data = linkedin_cache.get(profile_url) if linkedin_cache is not None else None

    if profile_data is None:
        profile_data = await get_linkedin_profile(profile_url)
        if linkedin_cache is not None:
            linkedin_cache.set(profile_url, profile_data)

    if "error" in profile_data:
        logging.warning(f"获取LinkedIn资料失败: {profile_data['error']}")
        return profile_data

    return simplify_linkedin_profile(profile_data)


@tool(parse_docstring=True)
async def get_linkedin_profiles(profile_urls: list[str]) -> str:
    """
    Retrieve detailed profile information for multiple LinkedIn users in batch.

    Use this function to get comprehensive profile data for several LinkedIn users simultaneously.
    More efficient than individual requests. Ensure all URLs are valid LinkedIn profile URLs.

    Args:
        profile_urls: List of complete LinkedIn profile URLs,
            Must be properly formatted URLs. Invalid URLs may return errors or partial results

    Returns:
        YAML formatted string containing profile information for all users including name,
        position, experience, education, skills, and other publicly accessible LinkedIn data
    """
    logging.info(f"批量获取 {len(profile_urls)} 个LinkedIn资料")
    results = await memoed_get_linkedin_profile.abatch(profile_urls, config={"max_concurrency": MAX_CONCURRENCY})
    # dispatch_custom_event("thinking", f"{len(results)} LinkedIn profiles retrieved")
    return yaml.dump(results, indent=2)


if __name__ == "__main__":
    import dotenv

    dotenv.load_dotenv()

    async def main():
        data = await get_linkedin_profiles.ainvoke(
            input={
                "profile_urls": [
                    "http://www.linkedin.com/in/darienbates",
                    "http://www.linkedin.com/in/josesermeno/",
                ],
            }
        )
        logging.info(f"result:\n{data}\n")

    asyncio.run(main())
