import os
from typing import Sequence

from dotenv import load_dotenv
from loguru import logger
from pydantic import SecretStr


def load_env():
    load_dotenv()

    app_env = os.getenv("APP_ENV", "local")
    logger.debug(f"Loading environment for {app_env}")

    env_file = f".env.{app_env}"
    if os.path.exists(env_file):
        load_dotenv(env_file, override=True)
        logger.debug(f"Loaded environment variables from {env_file}")
    else:
        logger.debug(f"No specific environment file found for {app_env}. Using default variables.")


def from_env(
    key: str | Sequence[str],
    /,
    *,
    default: str | None = None,
    error_message: str | None = None,
) -> str | None:
    """Get a value from an environment variable."""
    if isinstance(key, (list, tuple)):
        for k in key:
            if k in os.environ:
                return os.environ[k]
    if isinstance(key, str) and key in os.environ:
        return os.environ[key]

    if isinstance(default, (str, type(None))):
        return default
    if error_message:
        raise ValueError(error_message)
    msg = (
        f"Did not find {key}, please add an environment variable"
        f" `{key}` which contains it, or pass"
        f" `{key}` as a named parameter."
    )
    raise ValueError(msg)


def secret_from_env(
    key: str | Sequence[str],
    /,
    *,
    default: str | None = None,
    error_message: str | None = None,
) -> SecretStr | None:
    return SecretStr(from_env(key, default=default, error_message=error_message))
