from langchain_core.messages import AIMessage, BaseMessage


def message_thinking_content(message: BaseMessage) -> str | None:
    """Get the thinking content of the message.

    Args:
        message: The message to extract thinking content from
    """
    text_parts = []
    # openai o-series model reasoning content
    if "reasoning" in message.additional_kwargs:
        reasoning = message.additional_kwargs["reasoning"]
        if isinstance(reasoning, dict):
            reasoning_summary = reasoning.get("summary", [])
            for summary in reasoning_summary:
                if isinstance(summary, dict) and "text" in summary:
                    text_parts.append(summary["text"])
        elif isinstance(reasoning, str):
            text_parts.append(reasoning)

    if isinstance(message.content, list):
        for block in message.content:
            if not isinstance(block, dict):
                continue

            block_type = block.get("type", None)
            if block_type == "thinking":
                # Google Gemini thinking block
                text_parts.append(block.get("thinking", ""))
            elif block_type == "reasoning_content":
                # anthropic claude thinking block
                reasoning_content = block.get("reasoning_content", {})
                text_parts.append(reasoning_content.get("text", ""))

    return "\n".join(text_parts) if text_parts else None


def message_content(message: BaseMessage) -> str:
    """Get the text content of the message，this function will add <think> tag if the message is thinking.
    if you don't want to show the thinking content, you can use message.text() instead.

    Args:
        message: The message to extract content from

    Returns:
        The text content of the message.
    """
    thinking = message_thinking_content(message)
    thinking = f"<think>\n{thinking}\n</think>\n" if thinking else ""
    return thinking + message.text()


def create_text_summary(text: str, max_length: int = 200) -> str:
    """Create a summary of text with character count information.

    Args:
        text: The original text to summarize
        max_length: Maximum length of the summary (default: 200)

    Returns:
        A formatted summary string with character count
    """
    if not text:
        return "Empty content"

    text_length = len(text)

    if text_length <= max_length:
        return f"{text}"

    # create summary by truncating and adding ellipsis
    summary = text[:max_length].rstrip()
    return f"{summary}... showing first {len(summary)} chars of {text_length} chars total"


def add_citations(response: AIMessage, add_reference_links: bool = False) -> str:
    """Add citations to response text based on grounding metadata"""
    text = response.text()
    grounding_metadata = response.response_metadata.get("grounding_metadata")
    if not grounding_metadata:
        return text

    supports = grounding_metadata.get("grounding_supports", [])
    chunks = grounding_metadata.get("grounding_chunks", [])

    # Sort supports by end_index in descending order to avoid shifting issues when inserting.
    sorted_supports = sorted(supports, key=lambda s: s.get("segment", {}).get("end_index", 0), reverse=True)

    for support in sorted_supports:
        segment = support.get("segment", {})
        segment_text = segment.get("text", "")
        grounding_chunk_indices = support.get("grounding_chunk_indices", [])

        start_index = text.find(segment_text)
        end_index = start_index + len(segment_text) if start_index != -1 else None

        if end_index is not None and grounding_chunk_indices:
            # Create citation string like [[1]](link1), [[2]](link2)
            citation_links = []
            for i in grounding_chunk_indices:
                if i < len(chunks):
                    chunk = chunks[i]
                    web_info = chunk.get("web", {})
                    uri = web_info.get("uri", "")
                    if uri:
                        citation_links.append(f"[[{i + 1}]]({uri})")

            if citation_links:
                citation_string = " " + ", ".join(citation_links)
                text = text[:end_index] + citation_string + text[end_index:]

    # collect all reference links from chunks and add to the end
    reference_links = []
    for i, chunk in enumerate(chunks):
        web_info = chunk.get("web", {})
        uri = web_info.get("uri", "")
        title = web_info.get("title", "")
        if uri:
            reference_links.append(f"[{i + 1}] [{title}]({uri})")

    # add reference links to the end of text
    if reference_links and add_reference_links:
        text += "\n\n**References:**\n" + "\n".join(reference_links)

    return text
