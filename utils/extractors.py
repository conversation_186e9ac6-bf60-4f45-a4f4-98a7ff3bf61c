import json
from typing import Optional

from langchain_core.messages import BaseMessage
from langchain_core.output_parsers import StrOutputParser
from langchain_core.output_parsers.format_instructions import (
    JSON_FORMAT_INSTRUCTIONS,
)
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig
from loguru import logger
from pydantic import BaseModel

from .models import init_model


def context_to_str(input: str | BaseMessage) -> str:
    if isinstance(input, BaseMessage):
        return input.text()
    return input


async def extract_structure_response(
    context: str | BaseMessage, schema: dict | type, model: Optional[str] = "gpt-4.1-mini"
) -> BaseModel | dict:
    structured_args = {}
    if model.startswith("gpt-5"):
        # gpt-5 json schema requires strict mode,
        # it will raise error for pydantic json schema do'nt contains additionalProperties field

        structured_args["method"] = "function_calling"

    llm = init_model(model or "gpt-4.1-mini", max_tokens=10240).with_structured_output(schema, **structured_args)

    # build prompt template based on whether schema is BaseModel
    is_basemodel = isinstance(schema, type) and issubclass(schema, BaseModel)
    json_schema = schema.model_json_schema() if is_basemodel else schema
    format_instructions = JSON_FORMAT_INSTRUCTIONS.format(schema=json.dumps(json_schema, ensure_ascii=False))
    base_message = (
        "Given the contextual information, extract out an object that matches the provided schema without any filtering or judgment."  # noqa: E501
        "Here is the content of the section:"
        f"\n----------------\n{context_to_str(context)}\n----------------\n"
        f"{format_instructions}"
    )

    # first attempt
    try:
        return await llm.ainvoke(
            input=base_message,
            config=RunnableConfig(run_name="extract_structure_response"),
        )
    except Exception as e:  # noqa: BLE001
        logger.info(f"extract_structure_response first attempt failed, retrying once: {str(e)}")

        # build retry message including previous exception to help the model self-correct
        retry_message = (
            base_message
            + "\n\nNote: The previous attempt failed with error."
            + " Please strictly follow the JSON schema and return a valid object.\n\n"
            + "And avoid the error: "
            + str(e)
        )
        # second attempt
        return await llm.ainvoke(
            input=retry_message,
            config=RunnableConfig(run_name="extract_structure_response"),
        )


async def extract_title(context: str | BaseMessage) -> str:
    llm = init_model("gpt-4.1-mini")

    prompt = ChatPromptTemplate.from_template(
        "Here is the content of the section:"
        "----------------\n{context}\n----------------"
        "Give a title that summarizes all of the unique entities, titles or themes found in the context. Title:"
    )
    chain = context_to_str | prompt | llm | StrOutputParser()
    return await chain.ainvoke(context, config=RunnableConfig(run_name="extract_title"))


async def extract_summary(context: str | BaseMessage) -> str:
    llm = init_model("gpt-4.1-mini")

    prompt = ChatPromptTemplate.from_template(
        "Here is the content of the section:"
        "----------------\n{context}\n----------------"
        "Summarize the key topics and entities of the section. Summary:"
    )
    chain = context_to_str | prompt | llm | StrOutputParser()
    summary = await chain.ainvoke(context, config=RunnableConfig(run_name="extract_summary"))
    # remove the "Summary:" prefix
    return summary.removeprefix("Summary:").strip()
