import random
import re

import ftfy
import orjson
from ftfy.fixes import remove_control_chars
from loguru import logger


def random_timeout():
    return random.randint(4000, 20000)


def transform_all_results_to_excel_format(all_results: dict):
    data_list = []
    for company, users in all_results.items():
        if users and len(users) > 0:
            for user in users:
                data_list.append(
                    [
                        company,
                        user.get("name", ""),
                        user.get("position", ""),
                        user.get("url", ""),
                        user.get("thinking", ""),
                        user.get("about", ""),
                        # 将 keywords 转换为字符串
                        ", ".join(user.get("keywords", [])),
                        user.get("isTarget", ""),
                    ]
                )

    return data_list


def group_data_by_company_name(data_list: list):
    data_dict = {}
    for data in data_list:
        company_name = data.get("company_name")
        if company_name:
            if company_name not in data_dict:
                data_dict[company_name] = []
            data_dict[company_name].append(data)
    return data_dict


def clean_text(text):
    if text is None or text == "":
        return text
    # 用正则去除所有不可见字符（包括 \xad \xa0 \xae \x1b 等）保留常用可见字符（含中文、英文、数字、标点等）
    text = re.sub(r"[\x00-\x1f\x7f-\x9f\u00ad\u00a0\u00ae\u001b]", "", text)
    # 使用 ftfy 修正 Unicode 错误并清理特殊字符
    return remove_control_chars(ftfy.fix_text(text))


def clean_dict(data):
    """
    清理字典中的特殊字符
    """
    if data is None:
        return data
    result = data
    if isinstance(data, dict):
        result = {k: clean_dict(v) for k, v in data.items()}
    elif isinstance(data, list):
        result = [clean_dict(i) for i in data]
    elif isinstance(data, str):
        result = clean_text(data)
    return orjson.loads(orjson.dumps(result))


def main():
    data_arr = [
        {
            "name": "Ryan Delaney",
            "position": "Vice President Canada",
            "company_name": "Cisco",
            "url": "http://www.linkedin.com/in/ryan-delaney-a654082a",
            "thinking": "作为副总",
        },
        {
            "name": "Philip Passudetti",
            "position": "Chief Executive Officer",
            "company_name": "SSSSS",
            "url": "http://www.linkedin.com/in/philip-passudetti-4a170014",
            "thinking": "作为CEO。",
        },
    ]
    logger.info("data_arr", data_arr)
    logger.info(group_data_by_company_name(data_arr))


if __name__ == "__main__":
    main()
