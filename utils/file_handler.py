import csv
import json
import logging
import os
from pathlib import Path

import pandas as pd
from openpyxl import load_workbook
from openpyxl.styles import Alignment


def save_json_file(data, file_path: str):
    """写入json文件"""
    check_exists(os.path.dirname(file_path))
    try:
        with open(file_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    except Exception as e:
        logging.error(f"save json file '{file_path}' failed: {e}")


def json_to_excel(data, column_names: list, file_path: str):
    """写入excel文件"""
    check_exists(os.path.dirname(file_path))
    try:
        # Check if data is a list of lists (not a list of dicts)
        if data and isinstance(data, list) and isinstance(data[0], list):
            # For list of lists, create DataFrame with explicit column names
            df = pd.DataFrame(data, columns=column_names)
        else:
            # For list of dicts, create DataFrame and then select columns
            df = pd.DataFrame(data)
            df = df[column_names]

        df.to_excel(file_path, index=False)

        wb = load_workbook(file_path)
        ws = wb.active

        column_widths = {
            "A": 20,  # Company
            "B": 20,  # Name
            "C": 30,  # Position
            "D": 40,  # Linkedin  链接, 超链接，可以点击
            "E": 50,  # Thinking
            "F": 20,  # About
            "G": 20,  # Keywords
            "H": 15,  # Is Target
        }

        # 设置 Linkedin 列为超链接
        for row in range(2, ws.max_row + 1):  # type: ignore
            cell = ws[f"D{row}"]  # type: ignore
            url = cell.value
            if url and url.startswith("http"):
                cell.hyperlink = url
                cell.style = "Hyperlink"  # 应用超链接样式

        for col, width in column_widths.items():
            ws.column_dimensions[col].width = width  # type: ignore
            for cell in ws[col]:  # type: ignore
                cell.alignment = Alignment(wrap_text=True, vertical="top", horizontal="left")

        wb.save(file_path)

    except Exception as e:
        logging.error(f"save excel file '{file_path}' failed: {e}")


def load_json_file(file_path: str | Path):
    """读取json文件内容"""
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            return json.load(file)
    except Exception as e:
        logging.error(f"open file '{file_path}' failed: {e}")
        return None


def load_file(file_path: str | Path):
    """读取文件内容"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return f.read().strip()
    except Exception as e:
        logging.error(f"open file '{file_path}' failed: {e}")
        return None


def load_csv_file(file_path: str):
    """读取csv文件内容"""
    try:
        return pd.read_csv(file_path)
    except Exception as e:
        logging.error(f"open file '{file_path}' failed: {e}")
        return None


def check_exists(target_dir):
    """检查目标文件夹是否存在，不存在则创建"""
    try:
        if target_dir and not os.path.exists(target_dir):
            os.makedirs(target_dir)
    except OSError as e:
        logging.error(f"make dir '{target_dir}' failed: {e}")


# get file type by file name
def get_file_type(file_name: str):
    """获取文件类型"""
    return os.path.splitext(file_name)[1]


# get .xlsx or .xls file content
def get_excel_content(file_path: str):
    """获取excel文件内容"""
    try:
        return pd.read_excel(file_path)
    except Exception as e:
        logging.error(f"read excel file '{file_path}' failed: {e}")
        return None


# Get all files with specific extension in a directory recursively
def get_dir_children_file_list_by_file_type(directory: str, file_extensions: list = [".xlsx", ".xls"]):
    """
    Get all files with specific extensions in a directory and its subdirectories

    Args:
        directory: Directory path
        file_extensions: List of file extensions to filter (e.g., ['.xlsx', '.xls'])
                         Default is ['.xlsx', '.xls']

    Returns:
        List of tuples containing (relative_path, filename) with specified extensions
    """
    # Convert single extension string to list if needed
    if isinstance(file_extensions, str):
        file_extensions = [file_extensions]

    # Ensure all extensions start with a dot
    file_extensions = [ext if ext.startswith(".") else f".{ext}" for ext in file_extensions]

    logging.info(f"Searching for files with extensions {file_extensions} in {directory}")

    try:
        if not os.path.exists(directory):
            logging.warning(f"Directory '{directory}' does not exist")
            return []

        result = []

        # Walk through all subdirectories
        for root, _, files in os.walk(directory):
            # Get relative path from the base directory
            rel_path = os.path.relpath(root, directory)
            if rel_path == ".":
                rel_path = ""

            # Filter files by extension
            for file in files:
                file_ext = os.path.splitext(file)[1].lower()
                if file_ext in [ext.lower() for ext in file_extensions]:
                    logging.info(f"Found matching file: {os.path.join(root, file)}")
                    result.append((rel_path, file))

        return result
    except Exception as e:
        logging.error(f"Error getting files from directory '{directory}': {e}")
        return []


def csv_to_json(csv_file_path: str, save_file: bool = False):
    try:
        with open(csv_file_path, mode="r", encoding="utf-8") as file:
            reader = csv.DictReader(file)
            data = [row for row in reader]
        if save_file and os.path.exists(csv_file_path):
            json_file_path = csv_file_path.replace(".csv", ".json")
            save_json_file(data, json_file_path)
        return json.dumps(data, ensure_ascii=False, indent=2)
    except Exception as e:
        logging.error(f"convert csv file '{csv_file_path}' to json failed: {e}")
        return None
