"""Custom cache tool that extends diskcache.Cache with enhanced memoize functionality.

Features:
1. Uses parent class memoize by default
2. Disables cache when APP_ENV=development
3. Supports custom key generation function via key parameter
"""

import contextvars
import functools
import inspect
import os
from contextlib import contextmanager
from typing import Any, Callable, Optional, Set, Union

from diskcache import <PERSON><PERSON> as DiskCache
from diskcache.core import EN<PERSON><PERSON><PERSON>, args_to_key, full_name
from langchain_core.utils.env import env_var_is_set

# global context variable to control cache skipping
_skip_cache_context: contextvars.ContextVar[bool] = contextvars.ContextVar("skip_cache", default=False)


@contextmanager
def skip_cache():
    """Context manager to temporarily skip cache for all memoized functions.

    Usage:
        with skip_cache():
            result = some_cached_function()  # this call will skip cache
    """
    token = _skip_cache_context.set(True)
    try:
        yield
    finally:
        _skip_cache_context.reset(token)


def _is_instance_method(func):
    try:
        sig = inspect.signature(func)
        params = list(sig.parameters.keys())
        return len(params) > 0 and params[0] == "self"
    except (ValueError, TypeError):
        return False


class Cache(DiskCache):
    """Enhanced Cache that extends diskcache.Cache with custom memoize functionality."""

    def __init__(self, *args, cache_enabled_env_var: str = "CACHE_ENABLED", **kwargs):
        super().__init__(*args, **kwargs)
        self.cache_enabled_env_var = cache_enabled_env_var

    @property
    def cache_disabled(self) -> bool:
        env_disabled = self.cache_enabled_env_var in os.environ and not env_var_is_set(self.cache_enabled_env_var)
        return env_disabled or _skip_cache_context.get()

    def memoize(
        self,
        name: Optional[str] = None,
        typed: bool = False,
        expire: Optional[float] = None,
        tag: Optional[str] = None,
        ignore: Union[Set[str], tuple] = (),
        key: Optional[Callable] = None,
    ):
        """Enhanced memoizing cache decorator.

        Extends parent's memoize with:
        - Development environment bypass (APP_ENV=development)
        - Custom key generation function support
        - Context-based cache skipping via skip_cache() context manager

        Args:
            name: name given for callable (default None, automatic)
            typed: cache different types separately (default False)
            expire: seconds until arguments expire (default None, no expiry)
            tag: text to associate with arguments (default None)
            ignore: positional or keyword args to ignore (default ())
            key: custom function to generate cache key from args and kwargs

        Returns:
            callable decorator
        """

        super_memoize = super().memoize(name, typed, expire, tag, ignore)

        def decorator(func):
            super_wrapper = super_memoize(func)
            is_instance_method = _is_instance_method(func)
            base = (full_name(func),) if name is None else (name,)

            def __cache_key__(*args, **kwargs):
                """Make key for cache given function arguments."""
                _args = args[1:] if is_instance_method else args
                if key:
                    return key(*_args, **kwargs)
                return args_to_key(base, _args, kwargs, typed, ignore)

            super_wrapper.__cache_key__ = __cache_key__

            if inspect.iscoroutinefunction(func):

                @functools.wraps(func)
                async def wrapper(*args, _skip_cache: bool = False, **kwargs) -> Any:
                    """Async wrapper for callable to cache arguments and return values."""
                    if self.cache_disabled or _skip_cache:
                        return await func(*args, **kwargs)
                    cache_key = wrapper.__cache_key__(*args, **kwargs)
                    result = self.get(cache_key, default=ENOVAL, retry=True)

                    if result is ENOVAL:
                        result = await func(*args, **kwargs)
                        # only set cache if expire is None or > 0 (following diskcache pattern)
                        if expire is None or expire > 0:
                            self.set(cache_key, result, expire, tag=tag, retry=True)

                    return result

            else:

                @functools.wraps(func)
                def wrapper(*args, _skip_cache: bool = False, **kwargs) -> Any:
                    """Sync wrapper for callable to cache arguments and return values."""
                    if self.cache_disabled or _skip_cache:
                        return func(*args, **kwargs)

                    return super_wrapper(*args, **kwargs)

            wrapper.__cache_key__ = __cache_key__
            wrapper.skip_cache = func
            return wrapper

        return decorator
